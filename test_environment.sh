#!/bin/bash

# 环境测试脚本
# 检查yaslc抓包脚本运行所需的环境和依赖

echo "========== yaslc抓包环境测试 =========="
echo "测试时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试结果统计
PASS_COUNT=0
FAIL_COUNT=0

# 测试函数
test_item() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    printf "%-40s" "$test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        if [ "$expected_result" = "pass" ]; then
            echo -e "${GREEN}[PASS]${NC}"
            ((PASS_COUNT++))
        else
            echo -e "${RED}[FAIL]${NC}"
            ((FAIL_COUNT++))
        fi
    else
        if [ "$expected_result" = "fail" ]; then
            echo -e "${GREEN}[PASS]${NC}"
            ((PASS_COUNT++))
        else
            echo -e "${RED}[FAIL]${NC}"
            ((FAIL_COUNT++))
        fi
    fi
}

# 1. 检查基本命令
echo -e "${BLUE}1. 检查基本命令${NC}"
test_item "tcpdump命令" "command -v tcpdump" "pass"
test_item "ip命令" "command -v ip" "pass"
test_item "kill命令" "command -v kill" "pass"
test_item "ps命令" "command -v ps" "pass"
test_item "mkdir命令" "command -v mkdir" "pass"
echo ""

# 2. 检查权限
echo -e "${BLUE}2. 检查权限${NC}"
test_item "root权限" "[ \$EUID -eq 0 ]" "pass"
test_item "tcpdump执行权限" "[ -x /usr/sbin/tcpdump ]" "pass"
echo ""

# 3. 检查yaslc程序
echo -e "${BLUE}3. 检查yaslc程序${NC}"
test_item "yaslc程序存在" "[ -f ./run/yaslc ]" "pass"
test_item "yaslc程序可执行" "[ -x ./run/yaslc ]" "pass"
test_item "配置文件存在" "[ -f ./run/config.ini ]" "pass"
test_item "libipc.so存在" "[ -f ./run/libipc.so ]" "pass"
echo ""

# 4. 检查网络接口
echo -e "${BLUE}4. 检查网络接口${NC}"
echo "可用的网络接口："
ip link show | grep -E "^[0-9]+:" | awk '{print $2}' | sed 's/://' | while read interface; do
    if [ "$interface" != "lo" ]; then
        echo "  - $interface"
    fi
done

# 从配置文件读取接口
if [ -f "./run/config.ini" ]; then
    CONFIG_INTERFACE=$(grep "^IF" ./run/config.ini | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' ')
    if [ ! -z "$CONFIG_INTERFACE" ]; then
        test_item "配置的网络接口($CONFIG_INTERFACE)" "ip link show $CONFIG_INTERFACE" "pass"
    fi
fi
echo ""

# 5. 检查目录权限
echo -e "${BLUE}5. 检查目录权限${NC}"
test_item "/root目录可写" "[ -w /root ]" "pass"
test_item "/var/log目录可写" "[ -w /var/log ]" "pass"

# 测试创建抓包目录
if mkdir -p /root/pcaps 2>/dev/null; then
    test_item "创建抓包目录" "true" "pass"
    test_item "抓包目录可写" "[ -w /root/pcaps ]" "pass"
else
    test_item "创建抓包目录" "false" "pass"
fi
echo ""

# 6. 检查磁盘空间
echo -e "${BLUE}6. 检查磁盘空间${NC}"
ROOT_SPACE=$(df /root | tail -1 | awk '{print $4}')
if [ "$ROOT_SPACE" -gt 1048576 ]; then  # 1GB
    test_item "磁盘空间充足(>1GB)" "true" "pass"
else
    test_item "磁盘空间充足(>1GB)" "false" "pass"
    echo -e "${YELLOW}  警告: /root分区可用空间不足1GB${NC}"
fi
echo ""

# 7. 测试tcpdump功能
echo -e "${BLUE}7. 测试tcpdump功能${NC}"
if [ "$EUID" -eq 0 ]; then
    # 测试tcpdump是否能正常工作
    timeout 2 tcpdump -i any -c 1 >/dev/null 2>&1
    if [ $? -eq 0 ] || [ $? -eq 124 ]; then  # 124是timeout的退出码
        test_item "tcpdump功能测试" "true" "pass"
    else
        test_item "tcpdump功能测试" "false" "pass"
    fi
else
    echo "  跳过tcpdump功能测试（需要root权限）"
fi
echo ""

# 8. 检查脚本文件
echo -e "${BLUE}8. 检查脚本文件${NC}"
test_item "capture_yaslc.sh存在" "[ -f ./capture_yaslc.sh ]" "pass"
test_item "capture_yaslc.sh可执行" "[ -x ./capture_yaslc.sh ]" "pass"
test_item "quick_capture.sh存在" "[ -f ./quick_capture.sh ]" "pass"
test_item "quick_capture.sh可执行" "[ -x ./quick_capture.sh ]" "pass"
echo ""

# 9. 检查系统资源
echo -e "${BLUE}9. 检查系统资源${NC}"
MEMORY_MB=$(free -m | grep "^Mem:" | awk '{print $2}')
if [ "$MEMORY_MB" -gt 512 ]; then
    test_item "内存充足(>512MB)" "true" "pass"
else
    test_item "内存充足(>512MB)" "false" "pass"
fi

CPU_COUNT=$(nproc)
if [ "$CPU_COUNT" -gt 0 ]; then
    test_item "CPU核心数检测" "true" "pass"
else
    test_item "CPU核心数检测" "false" "pass"
fi
echo ""

# 10. 显示配置信息
echo -e "${BLUE}10. 配置信息${NC}"
if [ -f "./run/config.ini" ]; then
    echo "配置文件内容："
    cat ./run/config.ini | grep -v "^#" | grep -v "^$" | while read line; do
        echo "  $line"
    done
else
    echo "  配置文件不存在"
fi
echo ""

# 显示测试结果
echo "========== 测试结果 =========="
echo -e "通过: ${GREEN}$PASS_COUNT${NC}"
echo -e "失败: ${RED}$FAIL_COUNT${NC}"
echo -e "总计: $((PASS_COUNT + FAIL_COUNT))"

if [ $FAIL_COUNT -eq 0 ]; then
    echo -e "${GREEN}✓ 环境检查通过，可以运行抓包脚本${NC}"
    echo ""
    echo "建议的运行命令："
    echo "  sudo ./quick_capture.sh          # 快速启动"
    echo "  sudo ./capture_yaslc.sh          # 完整功能"
    echo "  sudo ./capture_yaslc.sh --help   # 查看帮助"
else
    echo -e "${RED}✗ 环境检查失败，请解决上述问题后再运行${NC}"
    echo ""
    echo "常见解决方案："
    echo "  1. 以root权限运行: sudo $0"
    echo "  2. 安装tcpdump: sudo apt install tcpdump"
    echo "  3. 编译yaslc程序: ./build.sh"
    echo "  4. 检查网络接口: ip link show"
fi

echo ""
echo "如需帮助，请查看: 抓包脚本使用说明.md"
