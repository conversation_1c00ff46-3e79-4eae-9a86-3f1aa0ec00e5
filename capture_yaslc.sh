#!/bin/bash

# 抓包脚本：启动yaslc程序的同时开始抓包，当程序崩溃或退出时停止抓包
# 用途：捕获会使程序崩溃的流量

# 配置参数
YASLC_PATH="./run/yaslc"                    # yaslc程序路径
PCAP_DIR="/root/pcaps"                      # 抓包文件保存目录
INTERFACE="ens33"                           # 网络接口（从config.ini读取）
ROTATE_INTERVAL=300                         # 文件轮转间隔（秒），5分钟
MAX_FILES=100                               # 最大保留文件数
TCPDUMP_FILTER=""                           # 抓包过滤器，可根据需要修改

# 日志文件
LOG_FILE="/var/log/yaslc_capture.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    log "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    log "${RED}[ERROR]${NC} $1"
}

log_success() {
    log "${GREEN}[SUCCESS]${NC} $1"
}

# 清理函数
cleanup() {
    log_info "正在清理进程..."
    
    # 停止tcpdump
    if [ ! -z "$TCPDUMP_PID" ] && kill -0 "$TCPDUMP_PID" 2>/dev/null; then
        log_info "停止tcpdump进程 (PID: $TCPDUMP_PID)"
        kill -TERM "$TCPDUMP_PID" 2>/dev/null
        sleep 2
        if kill -0 "$TCPDUMP_PID" 2>/dev/null; then
            kill -KILL "$TCPDUMP_PID" 2>/dev/null
        fi
    fi
    
    # 停止yaslc
    if [ ! -z "$YASLC_PID" ] && kill -0 "$YASLC_PID" 2>/dev/null; then
        log_info "停止yaslc进程 (PID: $YASLC_PID)"
        kill -TERM "$YASLC_PID" 2>/dev/null
        sleep 2
        if kill -0 "$YASLC_PID" 2>/dev/null; then
            kill -KILL "$YASLC_PID" 2>/dev/null
        fi
    fi
    
    log_info "清理完成"
    exit 0
}

# 信号处理
trap cleanup SIGINT SIGTERM

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查tcpdump
    if ! command -v tcpdump &> /dev/null; then
        log_error "tcpdump未安装，请先安装tcpdump"
        exit 1
    fi
    
    # 检查yaslc程序
    if [ ! -f "$YASLC_PATH" ]; then
        log_error "yaslc程序不存在: $YASLC_PATH"
        exit 1
    fi
    
    if [ ! -x "$YASLC_PATH" ]; then
        log_error "yaslc程序不可执行: $YASLC_PATH"
        exit 1
    fi
    
    # 检查网络接口
    if ! ip link show "$INTERFACE" &> /dev/null; then
        log_error "网络接口不存在: $INTERFACE"
        log_info "可用的网络接口："
        ip link show | grep -E "^[0-9]+:" | awk '{print $2}' | sed 's/://'
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建目录
create_directories() {
    log_info "创建目录..."
    
    if [ ! -d "$PCAP_DIR" ]; then
        mkdir -p "$PCAP_DIR"
        if [ $? -eq 0 ]; then
            log_success "创建抓包目录: $PCAP_DIR"
        else
            log_error "创建抓包目录失败: $PCAP_DIR"
            exit 1
        fi
    fi
    
    # 创建日志目录
    LOG_DIR=$(dirname "$LOG_FILE")
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
    fi
}

# 清理旧文件
cleanup_old_files() {
    local file_count=$(ls -1 "$PCAP_DIR"/*.pcap 2>/dev/null | wc -l)
    if [ "$file_count" -gt "$MAX_FILES" ]; then
        log_info "清理旧的抓包文件，当前文件数: $file_count，最大保留: $MAX_FILES"
        ls -1t "$PCAP_DIR"/*.pcap | tail -n +$((MAX_FILES + 1)) | xargs rm -f
        log_success "清理完成"
    fi
}

# 读取配置文件
read_config() {
    local config_file="./run/config.ini"
    if [ -f "$config_file" ]; then
        # 读取网络接口配置
        local config_interface=$(grep "^IF" "$config_file" | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' ')
        if [ ! -z "$config_interface" ]; then
            INTERFACE="$config_interface"
            log_info "从配置文件读取网络接口: $INTERFACE"
        fi
    fi
}

# 启动yaslc程序
start_yaslc() {
    log_info "启动yaslc程序..."
    
    cd "$(dirname "$YASLC_PATH")"
    
    # 启动yaslc程序
    "$YASLC_PATH" &
    YASLC_PID=$!
    
    # 等待程序启动
    sleep 2
    
    # 检查程序是否正常启动
    if kill -0 "$YASLC_PID" 2>/dev/null; then
        log_success "yaslc程序启动成功 (PID: $YASLC_PID)"
        return 0
    else
        log_error "yaslc程序启动失败"
        return 1
    fi
}

# 启动抓包
start_capture() {
    log_info "启动抓包..."
    
    # 生成抓包文件名
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local pcap_file="$PCAP_DIR/yaslc_capture_${timestamp}.pcap"
    
    # 构建tcpdump命令
    local tcpdump_cmd="tcpdump -i $INTERFACE -w $pcap_file -G $ROTATE_INTERVAL -W $MAX_FILES"
    
    # 添加过滤器（如果有）
    if [ ! -z "$TCPDUMP_FILTER" ]; then
        tcpdump_cmd="$tcpdump_cmd $TCPDUMP_FILTER"
    fi
    
    log_info "执行抓包命令: $tcpdump_cmd"
    
    # 启动tcpdump
    $tcpdump_cmd &
    TCPDUMP_PID=$!
    
    # 等待tcpdump启动
    sleep 2
    
    # 检查tcpdump是否正常启动
    if kill -0 "$TCPDUMP_PID" 2>/dev/null; then
        log_success "抓包启动成功 (PID: $TCPDUMP_PID)"
        log_info "抓包文件保存到: $PCAP_DIR"
        log_info "文件轮转间隔: ${ROTATE_INTERVAL}秒"
        return 0
    else
        log_error "抓包启动失败"
        return 1
    fi
}

# 监控yaslc程序状态
monitor_yaslc() {
    log_info "开始监控yaslc程序状态..."
    
    while true; do
        if ! kill -0 "$YASLC_PID" 2>/dev/null; then
            log_warn "检测到yaslc程序已退出或崩溃"
            break
        fi
        sleep 5
    done
    
    log_info "yaslc程序监控结束"
}

# 主函数
main() {
    log_info "========== yaslc抓包脚本启动 =========="
    log_info "脚本版本: 1.0"
    log_info "启动时间: $(date)"
    
    # 检查是否以root权限运行
    if [ "$EUID" -ne 0 ]; then
        log_error "请以root权限运行此脚本（需要抓包权限）"
        exit 1
    fi
    
    # 读取配置
    read_config
    
    # 检查依赖
    check_dependencies
    
    # 创建目录
    create_directories
    
    # 清理旧文件
    cleanup_old_files
    
    # 启动yaslc程序
    if ! start_yaslc; then
        log_error "yaslc程序启动失败，退出"
        exit 1
    fi
    
    # 启动抓包
    if ! start_capture; then
        log_error "抓包启动失败，清理并退出"
        cleanup
        exit 1
    fi
    
    log_success "yaslc程序和抓包都已启动，开始监控..."
    
    # 监控yaslc程序
    monitor_yaslc
    
    # yaslc程序退出后，清理资源
    log_info "yaslc程序已退出，正在停止抓包..."
    cleanup
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -i, --interface     指定网络接口（默认从config.ini读取）"
    echo "  -d, --dir           指定抓包文件保存目录（默认: /root/pcaps）"
    echo "  -t, --interval      指定文件轮转间隔（秒，默认: 300）"
    echo "  -f, --filter        指定tcpdump过滤器"
    echo "  -m, --max-files     最大保留文件数（默认: 100）"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置"
    echo "  $0 -i eth0 -d /tmp/pcaps             # 指定接口和目录"
    echo "  $0 -f \"host *************\"          # 添加过滤器"
    echo "  $0 -t 600 -m 50                     # 10分钟轮转，保留50个文件"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -i|--interface)
            INTERFACE="$2"
            shift 2
            ;;
        -d|--dir)
            PCAP_DIR="$2"
            shift 2
            ;;
        -t|--interval)
            ROTATE_INTERVAL="$2"
            shift 2
            ;;
        -f|--filter)
            TCPDUMP_FILTER="$2"
            shift 2
            ;;
        -m|--max-files)
            MAX_FILES="$2"
            shift 2
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 运行主函数
main
