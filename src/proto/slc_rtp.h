#include <map>
#include <functional>
#include <string>
#include <memory>
#include <netinet/in.h>

#include "slc_common.h"
#include "slc_nalu.h"
#include "rtp_h264_packer.h"
#include "rtp_h265_packer.h"
#include "slc_register.h"
#include "slc_observer.h"
#include "slc_singleton.h"
#include "slc_sdp.h"
#include "slc_flow.h"
#include "framework/slc_config.h"

#ifndef _SLC_RTP_H
#define _SLC_RTP_H

struct RtpPortInfo {
public:
  uint32_t port_src = 0;
  uint32_t port_dst = 0;
  int      payload_type = 0;
  bool     operator==(const RtpPortInfo &info_) const {
    return ((this->port_dst == info_.port_dst && this->port_src == info_.port_src) ||
               (this->port_src == info_.port_dst && this->port_dst == info_.port_src)) &&
           this->payload_type == info_.payload_type;
  }
  friend bool operator<(const RtpPortInfo &k1, const RtpPortInfo &k2);
};

inline bool operator<(const RtpPortInfo &k1, const RtpPortInfo &k2) {
  if (k1.port_dst != k2.port_dst && k1.port_src != k2.port_dst)
    return k1.port_dst < k2.port_dst;
  if (k1.port_src < k2.port_src && k1.port_dst < k2.port_src) {
    return k1.port_src < k2.port_src;
  }
  if (k1.payload_type != k2.payload_type) {
    return k1.payload_type < k2.payload_type;
  }
  return false;
}

struct RtpFlowKey : RtpPortInfo {
  uint32_t ssrc = 0;
  bool     operator==(const RtpFlowKey &info_) const {
    return ((this->port_dst == info_.port_dst && this->port_src == info_.port_src) ||
               (this->port_src == info_.port_dst && this->port_dst == info_.port_src)) &&
           this->payload_type == info_.payload_type && this->ssrc == info_.ssrc;
  }
  friend bool operator<(const RtpFlowKey &k1, const RtpFlowKey &k2);
};

// 统一的RTP媒体映射键，支持多种查找方式
struct RtpMediaKey {
  enum KeyType {
    SSRC_ONLY = 1,           // 仅使用SSRC (情况1: RTSP+RTPoverRTSP)
    SSRC_PORT_XOR = 2,       // SSRC与端口异或 (情况2: RTSP+UDP RTP)
    PORT_ONLY = 3,           // 仅使用端口信息 (情况3: SIP+UDP RTP的备用方案)
    SSRC_PORT_PAIR = 4       // SSRC和端口对 (情况3: SIP+UDP RTP的主要方案)
  };

  KeyType key_type;
  uint32_t ssrc;
  uint32_t port_src;
  uint32_t port_dst;
  uint8_t payload_type;

  // 构造函数
  RtpMediaKey() : key_type(SSRC_ONLY), ssrc(0), port_src(0), port_dst(0), payload_type(0) {}

  // 仅SSRC的构造函数 (情况1)
  RtpMediaKey(uint32_t ssrc_val)
    : key_type(SSRC_ONLY), ssrc(ssrc_val), port_src(0), port_dst(0), payload_type(0) {}

  // SSRC与端口异或的构造函数 (情况2)
  RtpMediaKey(uint32_t ssrc_val, uint32_t port_val, KeyType type = SSRC_PORT_XOR)
    : key_type(type), ssrc(ssrc_val), port_src(port_val), port_dst(0), payload_type(0) {}

  // 完整信息的构造函数 (情况3)
  RtpMediaKey(uint32_t ssrc_val, uint32_t port_src_val, uint32_t port_dst_val, uint8_t payload_type_val)
    : key_type(SSRC_PORT_PAIR), ssrc(ssrc_val), port_src(port_src_val), port_dst(port_dst_val), payload_type(payload_type_val) {}

  bool operator<(const RtpMediaKey& other) const {
    if (key_type != other.key_type) return key_type < other.key_type;
    if (ssrc != other.ssrc) return ssrc < other.ssrc;
    if (port_src != other.port_src) return port_src < other.port_src;
    if (port_dst != other.port_dst) return port_dst < other.port_dst;
    return payload_type < other.payload_type;
  }

  bool operator==(const RtpMediaKey& other) const {
    return key_type == other.key_type && ssrc == other.ssrc &&
           port_src == other.port_src && port_dst == other.port_dst &&
           payload_type == other.payload_type;
  }
};

inline bool operator<(const RtpFlowKey &k1, const RtpFlowKey &k2) {
  if (k1.port_dst != k2.port_dst && k1.port_src != k2.port_dst)
    return k1.port_dst < k2.port_dst;
  if (k1.port_src < k2.port_src && k1.port_dst < k2.port_src) {
    return k1.port_src < k2.port_src;
  }
  if (k1.payload_type != k2.payload_type) {
    return k1.payload_type < k2.payload_type;
  }
  return k1.ssrc < k2.ssrc;
}

struct RtpMediaInfo {
  RtpMediaInfo(){
    this->rtpPayloadType = 0;
    this->mediaType = RtpMediaType::unknown;
    this->pack_mode = 0;
    this->sipStream = NULL;
    this->rtspStream = NULL;

  }
  uint8_t      rtpPayloadType;
  RtpMediaType mediaType;
  uint8_t      pack_mode;
  std::string  byteH264PPS;
  std::string  byteH264SPS;
  std::string  Q931CallingPartyNumber;
  std::string  Q931CalledPartyNumber;
  std::string  sip_from;
  std::string  sip_to;
  void        *sipStream = NULL;
  void        *rtspStream = NULL;
  void         cleanAll() {
    rtpPayloadType = 0;
    mediaType = RtpMediaType::unknown;
    pack_mode = 0;
    byteH264PPS.clear();
    byteH264SPS.clear();
    Q931CallingPartyNumber.clear();
    Q931CalledPartyNumber.clear();
  }
};

#define CACL_RTP_VERSION(octet) ((octet) >> 6)

/* Padding is the third bit; No need to shift, because true is any value
   other than 0! */
#define RTP_PADDING(octet) (((octet)&0x20) == 0x20 ? 1 : 0)

/* Extension bit is the fourth bit */
#define RTP_EXTENSION(octet) ((octet)&0x10)

/* CSRC count is the last four bits */
#define RTP_CSRC_COUNT(octet) ((octet)&0xF)

/* Marker is the first bit of the second octet */
#define RTP_MARKER(octet) ((octet)&0x80)

/* Payload type is the last 7 bits */
#define RTP_PAYLOAD_TYPE(octet) ((octet)&0x7F)

static uint8_t isValidMSRTPType(uint8_t payloadType) {
  switch (payloadType) {
    case 0:  /* G.711 u-Law */
    case 3:  /* GSM 6.10 */
    case 4:  /* G.723.1  */
    case 8:  /* G.711 A-Law */
    case 9:  /* G.722 */
    case 13: /* Comfort Noise */
    case 15:
    case 18:  /*G.729*/
    case 96:  /* Dynamic RTP */
    case 97:  /* Redundant Audio Data Payload */
    case 101: /* DTMF */
    case 103: /* SILK Narrowband */
    case 104: /* SILK Wideband */
    case 105: /* Dynamic RTP (amr)*/
    case 111: /* Siren */
    case 112: /* G.722.1 */
    case 113: /* Dynamic RTP (siren)*/
    case 114: /* RT Audio Wideband */
    case 115: /* RT Audio Narrowband */
    case 116: /* G.726 */
    case 117: /* G.722 */
    case 118: /* Comfort Noise Wideband */
    case 34:  /* H.263 [MS-H26XPF] */
    case 121: /* RT Video */
    case 122: /* H.264 [MS-H264PF] */
    case 123: /* H.264 FEC [MS-H264PF] */
    case 127: /* x-data */
      return (1 /* RTP */);
      break;

    case 200: /* RTCP PACKET SENDER */
    case 201: /* RTCP PACKET RECEIVER */
    case 202: /* RTCP Source Description */
    case 203: /* RTCP Bye */
    //RTP视频
    case 31: /* H.261 */
    case 98: /* Dynamic RTP (h263)*/
    case 99: /* Dynamic RTP (h264)*/
      return (2 /* RTCP */);
      break;

    default:
      return (0);
  }
}

int NaluCallback(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);
int H265NaluCallback(H265NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);

class RtpStream : public flow_info {
public:
  RtpStream(RtpFlowKey &flowKey, flow_info *flow_info);
  RtpStream(const RtpStream &rhs) : H264Unpacker_(NaluCallback, this), H265Unpacker_(H265NaluCallback, this) {}

  void enqueRtpStreamFragment(uint8_t *payload, uint32_t payload_len);

  int onNaluCallback(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);
  int onH265NaluCallback(H265NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);

  bool hasUserData() { return (user_ != NULL); }

  void *getUserData() { return user_; }

public:
  //rtp 流信息
  RtpMediaType MediaType = RtpMediaType::unknown;
  int          payload_type;         // rtp的type数字类型
  int          AdvancedCodingType_;  // rtp的真实编码类型
  int          frameSeq_ = 0;
  uint64_t     ssrc;
  uint32_t     current_rtp_timestamp = 0;  // 当前RTP时间戳

private:
  //h264 相关
  RtpH264Unpacker H264Unpacker_;
  //h265 相关
  RtpH265Unpacker H265Unpacker_;

  void *user_ = NULL;  // decoder指针 决定了改stream归属
  // FILE* pH264File_;
};

#define RTPKEEPER RtpKeeper::GetInstance()

class RtpKeeper : public ObserverSessionKeeper, public singleton<RtpKeeper> {
  friend class SlcProtoLoader<RtpKeeper>;

public:
  RtpKeeper();
  void         identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);
  void         dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);
  void         foreachRtpStreamer(std::function<int(RtpStream *)> func);
  // RtpMediaType getRtpMediaType(RtpStream &stream);
  RtpMediaType getRtpMediaType(flow_info *flow, uint8_t payload_type, uint32_t ssrc);



  // 新的统一映射方法
  void addUnifiedMediaBinding(const RtpMediaKey& key, const RtpMediaInfo& minfo);
  RtpMediaType getUnifiedMediaType(flow_info *flow, uint8_t payload_type, uint32_t ssrc);
  RtpMediaType getUnifiedMediaType(RtpStream &stream);

  // 辅助方法：根据不同情况创建RtpMediaKey
  RtpMediaKey createKeyForRtspTcp(uint32_t ssrc);  // 情况1: RTSP + RTP over RTSP
  RtpMediaKey createKeyForRtspUdp(uint32_t ssrc, uint32_t server_port);  // 情况2: RTSP + UDP RTP
  RtpMediaKey createKeyForSipUdp(uint32_t ssrc, uint32_t port_src, uint32_t port_dst, uint8_t payload_type);  // 情况3: SIP + UDP RTP

public:
  // 统一的RTP媒体映射表
  std::map<RtpMediaKey, RtpMediaInfo> map_unified_rtpMediaInfo_;
  std::map<std::pair<std::pair<std::string, std::string>, std::string>, RtpPortInfo> map_serverIp2rtpPortInfo_;
  // <<ip_src,ip_dst>,media_type>

  std::map<RtpFlowKey, std::shared_ptr<RtpStream>> map_streams_;
};

#endif