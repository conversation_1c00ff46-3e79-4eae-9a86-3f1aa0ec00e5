
#include "slc_rtsp.h"
#include "ipc/ipc.h"
#include "ipc/ipc_decoder.h"
#include "slc_common.h"
#include "slc_rtp.h"
#include "base64.h"

#include <cstdio>
#include <string.h>
#include <ctype.h>
#include <algorithm>
#include <vector>
#include <functional>
#include <map>
#include <memory>
#include <stdlib.h>
#include <strings.h>  // for strcasestr

#define RTSP_NEED_MORE 0
#define RTSP_ERROR -1


extern slc::Logger global_logger;
// ===================================  辅助函数 =====================================
static void rtsp_sdp_copy_one_item(char *result, int max_len, const uint8_t *value, uint16_t len) {
  int copy_len;
  copy_len = len;
  if (copy_len > max_len - 1)
    copy_len = max_len - 1;

  strncpy(result, (const char *)value, copy_len);
  result[copy_len] = 0;

  return;
}

static void dissect_rtsp_sdp_owner(const uint8_t *line, int line_len, rtsp_sdp_info_t *info) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }
    switch (black_num) {
      case 0:
        rtsp_sdp_copy_one_item(info->o_username, sizeof(info->o_username), line + index, black_index);
        break;
      case 1:
        rtsp_sdp_copy_one_item(info->o_sessionid, sizeof(info->o_sessionid), line + index, black_index);
        break;
      case 2:
        rtsp_sdp_copy_one_item(info->o_version, sizeof(info->o_version), line + index, black_index);
        break;
      case 3:
        rtsp_sdp_copy_one_item(info->o_network_type, sizeof(info->o_network_type), line + index, black_index);
        break;
      case 4:
        rtsp_sdp_copy_one_item(info->o_address_type, sizeof(info->o_address_type), line + index, black_index);

        if (line_len > index + black_index + 1) {
          rtsp_sdp_copy_one_item(
              info->o_address, sizeof(info->o_address), line + index + black_index + 1, line_len - index - black_index - 1);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

static void dissect_rtsp_sdp_time(const uint8_t *line, int line_len, rtsp_sdp_info_t *info) {
  int black_index;

  black_index = find_blank_space(line, line_len);
  if (black_index <= 0) {
    return;
  }

  rtsp_sdp_copy_one_item(info->t_time_start, sizeof(info->t_time_start), line, black_index);

  if (line_len > black_index + 1) {
    rtsp_sdp_copy_one_item(info->t_time_end, sizeof(info->t_time_end), line + black_index + 1, line_len - black_index - 1);
  }

  return;
}

static void dissect_rtsp_sdp_r_data(const uint8_t *line, int line_len, rtsp_sdp_info_t *info) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }

    switch (black_num) {
      case 0:
        rtsp_sdp_copy_one_item(info->r_repeat_interval, sizeof(info->r_repeat_interval), line + index, black_index);
        break;
      case 1:
        rtsp_sdp_copy_one_item(info->r_active_duration, sizeof(info->r_active_duration), line + index, black_index);
        break;
      case 2:
        if (line_len > index + black_index + 1) {
          rtsp_sdp_copy_one_item(info->r_offsets_from_start_time, sizeof(info->r_offsets_from_start_time),
              line + index + black_index + 1, line_len - index - black_index - 1);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }
  return;
}

static void dissect_rtsp_sdp_connnection_info(const uint8_t *line, int line_len, rtsp_sdp_info_t *info) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }

    switch (black_num) {
      case 0:
        rtsp_sdp_copy_one_item(info->c_network_type, sizeof(info->c_network_type), line + index, black_index);
        break;
      case 1:
        rtsp_sdp_copy_one_item(info->c_address_type, sizeof(info->c_address_type), line + index, black_index);

        if (line_len > index + black_index + 1) {
          rtsp_sdp_copy_one_item(
              info->c_address, sizeof(info->c_address), line + index + black_index + 1, line_len - index - black_index - 1);
        }
        break;
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }
  return;
}

static void find_rstp_sdp_media_payload(const uint8_t *start, int len, char *result, int max_len) {
  int  black_index;
  int  offset = 0;
  char num_str[32];
  int  copy_len;
  int  num;
  int  _len = 0;

  while (offset < len) {
    black_index = find_blank_space(start + offset, len - offset);
    if (black_index <= 0) {
      break;
    }
    copy_len = black_index >= 32 ? 31 : black_index;
    strncpy(num_str, (const char *)start + offset, copy_len);
    num_str[copy_len] = 0;

    num = atoi(num_str);
    if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < RTSP_MEDIA_PAYLOAD_LEN_MAX) {
      _len += snprintf(result + _len, max_len - _len, "%s;", rtsp_rtp_type_vals[num].strptr);
    }
    offset += black_index + 1;
  }

  if (offset < len) {
    copy_len = len - offset >= 32 ? 31 : len - offset;
    strncpy(num_str, (const char *)start + offset, copy_len);
    num_str[copy_len] = 0;

    num = atoi(num_str);
    if (num >= PT_PCMU && num <= PT_UNDF_127 && _len < RTSP_MEDIA_PAYLOAD_LEN_MAX) {
      _len += snprintf(result + _len, max_len - _len, "%s;", rtsp_rtp_type_vals[num].strptr);
    }
  }
}

static void dissect_rtsp_sdp_media(const uint8_t *line, int line_len, struct rtsp_sdp_m_info *info) {
  int black_num = 0;
  int black_index;
  int index = 0;

  while (index < line_len) {
    black_index = find_blank_space(line + index, line_len - index);
    if (black_index <= 0) {
      break;
    }

    switch (black_num) {
      case 0:
        rtsp_sdp_copy_one_item(info->m_type, sizeof(info->m_type), line + index, black_index);
        break;
      case 1:
        rtsp_sdp_copy_one_item(info->m_port, sizeof(info->m_port), line + index, black_index);
        break;
      case 2:
        rtsp_sdp_copy_one_item(info->m_proto, sizeof(info->m_proto), line + index, black_index);
        if (line_len > index + black_index + 1) {
          find_rstp_sdp_media_payload(
              line + index + black_index + 1, line_len - index - black_index - 1, info->m_payloads, sizeof(info->m_payloads));
        }

        return;  //return
      default:
        break;
    }
    black_num++;
    index += black_index + 1;
  }

  return;
}

// 安全的不区分大小写字符串查找函数，带边界检查
static const char* safe_strncasestr(const char* haystack, size_t haystack_len, const char* needle) {
  if (!haystack || !needle || haystack_len == 0)
    return NULL;

  size_t needle_len = strlen(needle);
  if (needle_len == 0 || needle_len > haystack_len)
    return NULL;

  // 创建一个临时的null结尾字符串用于搜索
  char* temp_haystack = (char*)malloc(haystack_len + 1);
  if (!temp_haystack)
    return NULL;

  memcpy(temp_haystack, haystack, haystack_len);
  temp_haystack[haystack_len] = '\0';

  char* result = strcasestr(temp_haystack, needle);
  const char* return_ptr = NULL;

  if (result) {
    // 计算在原始字符串中的偏移量
    size_t offset = result - temp_haystack;
    if (offset <= haystack_len - needle_len) {
      return_ptr = haystack + offset;
    }
  }

  free(temp_haystack);
  return return_ptr;
}

// ===================================  Rtsp解析器 =====================================


int RtspStream::dissect_rtsp_sdp(const uint8_t *payload, const uint32_t payload_len, rtsp_sdp_info_t *info) {
  uint8_t        f_in_media = 0;
  uint32_t       offset = 0;
  const uint8_t *line;
  int            line_len;
  char           type;
  char           delim;

  int attr_index = 0;
  int media_index = -1;
  int a_index = 0;
  line = payload;
  while (offset < payload_len) {
    if (media_index >= RTSP_SDP_MEDIA_MAX_NUM - 1)
      break;
    line_len = find_packet_line_end(line, payload_len - offset);

    /*
        * Line must contain at least e.g. "v=".
        */
    if (line_len < 2)
      break;

    type = *line;
    delim = *(line + 1);
    if (delim != '=')
      goto next_line;

    switch (type) {
      case 'v':
        rtsp_sdp_copy_one_item(info->v_version, sizeof(info->v_version), line + 2, line_len - 2);
        break;
      case 'o':
        //rtsp_sdp_copy_one_item(info->o_owner, sizeof(info->o_owner), line + 2, line_len - 2);
        dissect_rtsp_sdp_owner(line + 2, line_len - 2, info);
        break;
      case 's':
        rtsp_sdp_copy_one_item(info->s_name, sizeof(info->s_name), line + 2, line_len - 2);
        break;
      case 'i':
        //if (f_in_media)
        //    rtsp_sdp_copy_one_item(info->m_info[media_index].m_title, sizeof(info->m_info[media_index].m_title), line + 2, line_len - 2);
        //else
        rtsp_sdp_copy_one_item(info->i_info, sizeof(info->i_info), line + 2, line_len - 2);
        break;
      case 'u':
        rtsp_sdp_copy_one_item(info->u_uri, sizeof(info->u_uri), line + 2, line_len - 2);
        break;
      case 'e':
        rtsp_sdp_copy_one_item(info->e_email, sizeof(info->e_email), line + 2, line_len - 2);
        break;
      case 'p':
        rtsp_sdp_copy_one_item(info->p_phone, sizeof(info->p_phone), line + 2, line_len - 2);
        break;
      case 'c':
        //rtsp_sdp_copy_one_item(info->c_info, sizeof(info->c_info), line + 2, line_len - 2);
        dissect_rtsp_sdp_connnection_info(line + 2, line_len - 2, info);
        break;
      case 'b':
        rtsp_sdp_copy_one_item(info->b_bandwidths, sizeof(info->b_bandwidths), line + 2, line_len - 2);
        break;
      case 't':
        //rtsp_sdp_copy_one_item(info->t_time, sizeof(info->t_time), line + 2, line_len - 2);
        dissect_rtsp_sdp_time(line + 2, line_len - 2, info);
        break;
      case 'r':
        //rtsp_sdp_copy_one_item(info->r_repeattime, sizeof(info->r_repeattime), line + 2, line_len - 2);
        dissect_rtsp_sdp_r_data(line + 2, line_len - 2, info);
        break;
      case 'z':
        rtsp_sdp_copy_one_item(info->z_timezone_adjustment, sizeof(info->z_timezone_adjustment), line + 2, line_len - 2);
        break;
      case 'k':
        rtsp_sdp_copy_one_item(info->k_encryptionkey, sizeof(info->k_encryptionkey), line + 2, line_len - 2);
        break;
      case 'm':
        f_in_media = 1;
        media_index++;
        attr_index = 0;
        //rtsp_sdp_copy_one_item(info->m_info[media_index].m_media, sizeof(info->m_info[media_index].m_media), line + 2, line_len - 2);
        dissect_rtsp_sdp_media(line + 2, line_len - 2, &info->m_info[media_index]);
        break;

      //now only one attributes is writing
      case 'a':
        if (f_in_media) {
          if (attr_index >= RTSP_SDP_M_ATTR_MAX_NUM)
            break;
          rtsp_sdp_copy_one_item(info->m_info[media_index].a_attributes[attr_index],
              sizeof(info->m_info[media_index].a_attributes[attr_index]), line + 2, line_len - 2);
          attr_index++;
        } else {
          if (a_index > 20)
            break;
          rtsp_sdp_copy_one_item(info->session_attribute[a_index], sizeof(info->session_attribute), line + 2, line_len - 2);
          a_index++;
        }
        break;
      default:
        break;
    }
  next_line:
    offset += line_len + 2;
    line = &payload[offset];
  }
  //    write_rtp_sdp_log(flow, direction, info);

  return 0;
}

int RtspStream::process_rtsp_reply_transport(const uint8_t *line, int linelen) {
  char marker_client_port[] = "client_port=";
  char marker_server_port[] = "server_port=";
  char marker_ssrc[] = "ssrc=";
  char marker_interleaved[] = "interleaved=";

  char *input = strndup((char *)line, linelen);
  char *token = strtok(input, ":;");
  while (token != NULL) {
    if (strncmp(token, marker_client_port, sizeof marker_client_port - 1) == 0) {
      strncpy(transport_client_port_, token + sizeof(marker_client_port) - 1, sizeof(transport_client_port_));
    }

    if (strncmp(token, marker_server_port, sizeof marker_server_port - 1) == 0) {
      strncpy(transport_server_port_, token + sizeof(marker_server_port) - 1, sizeof(transport_server_port_));
    }

    // 解析 SSRC 参数，特别是在 TCP 模式下
    if (strncmp(token, marker_ssrc, sizeof marker_ssrc - 1) == 0) {
      char *ssrc_str = token + sizeof(marker_ssrc) - 1;
      // 将十六进制字符串转换为数值
      uint32_t parsed_ssrc = (uint32_t)strtoul(ssrc_str, NULL, 16);

      // 检查是否已经存在该SSRC，避免重复添加
      bool ssrc_exists = false;
      for (int i = 0; i < transport_ssrc_count; i++) {
        if (transport_ssrc[i] == parsed_ssrc) {
          ssrc_exists = true;
          break;
        }
      }

      // 如果SSRC不存在且还有空间，则添加到数组中
      if (!ssrc_exists && transport_ssrc_count < 12) {
        transport_ssrc[transport_ssrc_count] = parsed_ssrc;
        transport_ssrc_count++;
        printf("Parsed SSRC from Transport header: 0x%x (count: %d)\n", parsed_ssrc, transport_ssrc_count);
      }

      // 为了兼容性，将第一个SSRC赋值给sdp_.ssrc
      if (transport_ssrc_count > 0) {
        sdp_.ssrc = transport_ssrc[0];
      }
    }

    // 解析 interleaved 参数，用于 TCP 模式下的通道标识
    if (strncmp(token, marker_interleaved, sizeof marker_interleaved - 1) == 0) {
      char *interleaved_str = token + sizeof(marker_interleaved) - 1;
      printf("Parsed interleaved channels from Transport header: %s\n", interleaved_str);
      // interleaved 通常格式为 "2-3"，表示 RTP 和 RTCP 通道
      // 这里可以根据需要进一步解析具体的通道号
    }

    token = strtok(NULL, ":;");
  };

  if (strlen(transport_client_port_) > 0 && strlen(transport_server_port_) > 0) {
    transport_info_parsed_ = 1;
  }
  if (NULL != strstr((const char *)input, "RTP/AVP/TCP")) {
    rtp_over_rtsp_parsed_ = 1;
    // 在 TCP 模式下，如果解析到了 SSRC，标记为已解析传输信息
    if (transport_ssrc_count > 0) {
      transport_info_parsed_ = 1;
    }
  }
  free(input);

  return 0;
}

void RtspStream::process_rtsp_reply(const uint8_t *line, int linelen) {
  const uint8_t *lineend = line + linelen;
  const uint8_t *status = line;
  const uint8_t *status_start;
  uint32_t       status_i;

  /* status code */

  /* Skip protocol/version */
  while (status < lineend && !isspace(*status)) status++;
  /* Skip spaces */
  while (status < lineend && isspace(*status)) status++;

  /* Actual code number now */
  status_start = status;
  status_i = 0;
  while (status < lineend && isdigit(*status)) status_i = status_i * 10 + *status++ - '0';

  struct rtsp_point_value *transport_line = &rstp_head_[EM_RSTP_H_TRANSPORT];
  if (transport_line->ptr != NULL) {
    /* eg: Transport: RTP/AVP/UDP;unicast;client_port=54782-54783;server_port=45020-45021;ssrc=1DCBB018
            Transport: RTP/AVP/TCP;unicast;interleaved=2-3;ssrc=5c5c3fa;mode="PLAY"
    */
    process_rtsp_reply_transport(transport_line->ptr, transport_line->len);
  }

  status_code_ = status_i;
  response_code_ = status + 1;
  response_code_len_ = lineend > status ? lineend - status - 1 : 0;
  return;
}

void RtspStream::process_rtsp_request(const uint8_t *line, int linelen) {
  const uint8_t *lineend = line + linelen;
  uint32_t       ii;
  const uint8_t *url;
  const uint8_t *url_start;
  uint8_t       *tmp_url;

  const uint8_t *data = line;

  /* URL */
  url = line;
  /* Skip method name again */
  while (url < lineend && !isspace(*url)) url++;

  /* Skip spaces */
  while (url < lineend && isspace(*url)) url++;

  /* URL starts here */
  url_start = url;
  /* Scan to end of URL */
  while (url < lineend && !isspace(*url)) url++;

  request_uri_ = url_start;
  request_uri_len_ = url - url_start;

  return;
}
/*
* 解析rtsp头部的每一行头域，寻找空行位置
* 修复逻辑漏洞：确保在 head_len + sdp_len = payload_len 时能正确返回sdp_s
*/
const uint8_t *RtspStream::rtsp_parse_line_info(const uint8_t *payload, const uint16_t payload_len) {
  uint32_t a;
  uint16_t line_len = 0;
  uint8_t  parsed_lines = 0;
  const uint8_t          *line_ptr = payload;
  char                   *_header;
  struct rtsp_point_value ht_value;

  int            header_len;
  const uint8_t *sdp_s = NULL;
  int            i;

  // 参数检查
  if ((payload_len == 0) || (payload == NULL))
    return NULL;

  // 修复边界条件：使用 payload_len - 1 而不是 payload_len - 2
  // 确保能够处理到倒数第二个字节，以便检测 \r\n\r\n 序列
  for (a = 0; a <= payload_len - 2; a++) {
    // 检查是否找到 \r\n 序列
    if (a + 1 < payload_len && get_uint16_t(payload, a) == ntohs(0x0d0a)) {
      line_len = (uint16_t)(((unsigned long)&payload[a]) - ((unsigned long)line_ptr));

      // 检查是否为空行（连续的 \r\n\r\n）
      if (line_len == 0) {
        // 空行后面就是SDP数据
        if (a + 2 < payload_len) {
          sdp_s = &payload[a + 2];
        } else if (a + 2 == payload_len) {
          // 边界情况：SDP数据长度为0，但位置有效
          sdp_s = &payload[a + 2];
        }
        break;
      }

      // 处理第一行（请求行或响应行）
      if (parsed_lines == 0) {
        rstp_head_[EM_RTSP_H_HEADER].len = line_len;
        rstp_head_[EM_RTSP_H_HEADER].ptr = line_ptr;
      } else {
        // 处理头部字段行
        header_len = find_special_char(line_ptr, line_len, ':');
        if (header_len <= 0) {
          goto next_line;
        }

        // 提取头部字段名
        if (line_ptr[header_len - 1] == ' ')
          _header = strndup((const char *)line_ptr, header_len - 1);
        else
          _header = strndup((const char *)line_ptr, header_len);

        if (_header == NULL) {
          goto next_line;
        }

        strdown_inplace(_header);

        // 检查头部字段值是否存在
        if (header_len + 1 >= line_len) {
          free(_header);
          goto next_line;
        }

        // 提取头部字段值
        if (line_ptr[header_len + 1] == ' ') {
          ht_value.len = line_len - header_len - 2;
          ht_value.ptr = line_ptr + header_len + 2;
        } else {
          ht_value.len = line_len - header_len - 1;
          ht_value.ptr = line_ptr + header_len + 1;
        }

        // 查找匹配的头部字段类型
        for (i = EM_RSTP_H_ACCEPT; i < EM_RTSP_H_MAX; i++) {
          if (strcmp(_header, rtsp_header_data[i].head_type) == 0) {
            rstp_head_[i].len = ht_value.len;
            rstp_head_[i].ptr = ht_value.ptr;
            break;
          }
        }

        free(_header);
      }

    next_line:
      parsed_lines++;
      line_ptr = &payload[a + 2];
      line_len = 0;

      // 检查是否已到达数据末尾
      if ((a + 2) >= payload_len)
        break;

      a++; /* 跳过 \n，下次循环将处理下一行 */
    }
  }

  return sdp_s;
}

uint8_t RtspStream::is_rtsp_request_or_reply(const uint8_t *line, int linelen, rtsp_type_t *type) {
  uint32_t       ii;
  const uint8_t *token, *next_token;
  int            tokenlen;
  char           response_chars[4];

  /* Is this an RTSP reply? */
  if (linelen >= 5 && strncasecmp("RTSP/", (const char *)line, 5) == 0) {
    *type = RTSP_REPLY;

    return 1;
  }

  /*
     * Is this an RTSP request?
     * Check whether the line begins with one of the RTSP request
     * methods.
     */
  for (ii = 0; ii < RTSP_NMETHODS; ii++) {
    int len = strlen(rtsp_methods[ii]);
    if (linelen >= len && strncasecmp(rtsp_methods[ii], (const char *)line, len) == 0 && (len == linelen || isspace(line[len]))) {
      *type = RTSP_REQUEST;

      strncpy(request_method_, rtsp_methods[ii], len);
      return 1;
    }
  }

  /* Wasn't a request or a response */
  *type = RTSP_NOT_FIRST_LINE;
  return 0;
}

void RtspStream::createSsrcMediaBinding() {
  // 为当前 RTSP 流中解析到的 SSRC 建立媒体类型绑定
  if (transport_ssrc_count == 0) {
    return;  // 没有有效的 SSRC
  }

  // 验证 RTPKEEPER 指针
  if (!RTPKEEPER) {
    printf("createSsrcMediaBinding: RTPKEEPER is null\n");
    return;
  }

  // 遍历 SDP 信息中的媒体描述，为每个媒体类型建立 SSRC 绑定
  // 注意：在 TCP 模式下，通常每个媒体流（音频/视频）都有自己的 SSRC
  // 现在我们支持多个 SSRC，可以为每个媒体流分配对应的 SSRC

  for (int i = 0; i < sdp_.info.m_info_num && i < RTSP_SDP_MEDIA_MAX_NUM; i++) {
    RtpMediaInfo minfo;
    minfo.rtspStream = this;

    // 从媒体描述中获取媒体类型
    const char* media_type = sdp_.info.m_info[i].m_type;
    if (strlen(media_type) == 0) {
      continue;  // 跳过空的媒体类型
    }

    printf("Processing media type: %s\n", media_type);

    if (strstr(media_type, "video") != NULL) {
      // 默认视频类型，具体类型需要通过 rtpmap 确定
      minfo.mediaType = RtpMediaType::video_h264;  // 默认 H264

      // 检查 rtpmap 属性以确定具体的视频编码类型和 payload type
      for (int j = 0; j < sdp_.info.m_info[i].a_attr_num && j < RTSP_SDP_M_ATTR_MAX_NUM; j++) {
        const char* attr = sdp_.info.m_info[i].a_attributes[j];
        if (strstr(attr, "rtpmap") != NULL) {
          printf("Found rtpmap: %s\n", attr);

          // 从 rtpmap 属性中提取 payload type
          // 格式: "rtpmap:108 H265/90000"
          const char* colon_ptr = strchr(attr, ':');
          if (colon_ptr != NULL) {
            int payload_type = 0;
            if (sscanf(colon_ptr + 1, "%d", &payload_type) == 1) {
              if (payload_type >= 0 && payload_type <= 127) {
                minfo.rtpPayloadType = payload_type;
                printf("Extracted payload type: %d from rtpmap\n", payload_type);
              }
            }
          }

          if (strstr(attr, "H264") != NULL || strstr(attr, "h264") != NULL) {
            minfo.mediaType = RtpMediaType::video_h264;
          } else if (strstr(attr, "H265") != NULL || strstr(attr, "h265") != NULL ||
                     strstr(attr, "HEVC") != NULL || strstr(attr, "hevc") != NULL) {
            minfo.mediaType = RtpMediaType::video_h265;
          } else if (strstr(attr, "H263") != NULL || strstr(attr, "h263") != NULL) {
            minfo.mediaType = RtpMediaType::video_h263;
          }
        }
      }
    } else if (strstr(media_type, "audio") != NULL) {
      minfo.mediaType = RtpMediaType::audio;  // 默认音频类型

      // 检查具体的音频编码类型和 payload type
      for (int j = 0; j < sdp_.info.m_info[i].a_attr_num && j < RTSP_SDP_M_ATTR_MAX_NUM; j++) {
        const char* attr = sdp_.info.m_info[i].a_attributes[j];
        if (strstr(attr, "rtpmap") != NULL) {
          printf("Found audio rtpmap: %s\n", attr);

          // 从 rtpmap 属性中提取 payload type
          const char* colon_ptr = strchr(attr, ':');
          if (colon_ptr != NULL) {
            int payload_type = 0;
            if (sscanf(colon_ptr + 1, "%d", &payload_type) == 1) {
              if (payload_type >= 0 && payload_type <= 127) {
                minfo.rtpPayloadType = payload_type;
                printf("Extracted audio payload type: %d from rtpmap\n", payload_type);
              }
            }
          }

          if (strstr(attr, "G729") != NULL || strstr(attr, "g729") != NULL) {
            minfo.mediaType = RtpMediaType::audio_g729;
          } else if (strstr(attr, "PCMA") != NULL || strstr(attr, "pcma") != NULL ||
                     strstr(attr, "PCMU") != NULL || strstr(attr, "pcmu") != NULL) {
            minfo.mediaType = RtpMediaType::audio_g711;
          }
        }
      }
    } else {
      continue;  // 跳过不支持的媒体类型
    }

    if (i < transport_ssrc_count) {
      uint32_t current_ssrc = transport_ssrc[i];
      // 解析 transport_server_port_ 中的端口号
      uint32_t server_port = parseFirstPort(transport_server_port_);



      // 添加新的统一映射绑定
      if (rtp_over_rtsp_parsed_ == 1) {
        // 情况1: RTSP + RTP over RTSP (TCP模式)
        RtpMediaKey key = RTPKEEPER->createKeyForRtspTcp(current_ssrc);
        RTPKEEPER->addUnifiedMediaBinding(key, minfo);
        printf("Created unified media binding (RTSP TCP): SSRC=0x%x, MediaType=%d for %s\n",
               current_ssrc, (int)minfo.mediaType, media_type);
      } else if (server_port != 0) {
        // 情况2: RTSP + UDP RTP
        RtpMediaKey key = RTPKEEPER->createKeyForRtspUdp(current_ssrc, server_port);
        RTPKEEPER->addUnifiedMediaBinding(key, minfo);
        printf("Created unified media binding (RTSP UDP): SSRC=0x%x, ServerPort=%d, MediaType=%d for %s\n",
               current_ssrc, server_port, (int)minfo.mediaType, media_type);
      }

      printf("Created SSRC media binding: SSRC=0x%x, MediaType=%d, PayloadType=%d, ServerPort=%d for %s (media index: %d)\n",
             current_ssrc, (int)minfo.mediaType, minfo.rtpPayloadType, server_port, media_type, i);
    } else if (transport_ssrc_count > 0) {
      // 如果 SSRC 数量不够，使用第一个 SSRC
      uint32_t current_ssrc = transport_ssrc[0];
      uint32_t server_port = parseFirstPort(transport_server_port_);



      // 添加新的统一映射绑定
      if (rtp_over_rtsp_parsed_ == 1) {
        // 情况1: RTSP + RTP over RTSP (TCP模式)
        RtpMediaKey key = RTPKEEPER->createKeyForRtspTcp(current_ssrc);
        RTPKEEPER->addUnifiedMediaBinding(key, minfo);
        printf("Created unified media binding (RTSP TCP fallback): SSRC=0x%x, MediaType=%d for %s\n",
               current_ssrc, (int)minfo.mediaType, media_type);
      } else if (server_port != 0) {
        // 情况2: RTSP + UDP RTP
        RtpMediaKey key = RTPKEEPER->createKeyForRtspUdp(current_ssrc, server_port);
        RTPKEEPER->addUnifiedMediaBinding(key, minfo);
        printf("Created unified media binding (RTSP UDP fallback): SSRC=0x%x, ServerPort=%d, MediaType=%d for %s\n",
               current_ssrc, server_port, (int)minfo.mediaType, media_type);
      }

      printf("Created SSRC media binding (fallback): SSRC=0x%x, MediaType=%d, PayloadType=%d, ServerPort=%d for %s (media index: %d)\n",
             current_ssrc, (int)minfo.mediaType, minfo.rtpPayloadType, server_port, media_type, i);
    }

    // 继续处理下一个媒体流，不再 break
  }
}

// 辅助函数：从端口字符串中解析第一个端口号
uint32_t RtspStream::parseFirstPort(const char* port_str) const {
  if (!port_str || strlen(port_str) == 0) {
    return 0;
  }

  // 端口字符串格式通常是 "45020-45021"，我们需要解析第一个端口号
  char port_copy[16];
  strncpy(port_copy, port_str, sizeof(port_copy) - 1);
  port_copy[sizeof(port_copy) - 1] = '\0';

  // 查找 '-' 分隔符
  char* dash_pos = strchr(port_copy, '-');
  if (dash_pos) {
    *dash_pos = '\0';  // 截断字符串，只保留第一个端口号
  }

  // 转换为整数
  char* endptr;
  long port_val = strtol(port_copy, &endptr, 10);

  // 验证转换结果
  if (*endptr == '\0' && port_val > 0 && port_val <= 65535) {
    return (uint32_t)port_val;
  }

  return 0;
}

// SSRC 数组相关的辅助函数实现
uint32_t RtspStream::getSsrc(int index) const {
  if (index >= 0 && index < transport_ssrc_count) {
    return transport_ssrc[index];
  }
  return 0;  // 返回 0 表示无效的 SSRC
}

int RtspStream::getSsrcCount() const {
  return transport_ssrc_count;
}

void RtspStream::clearSsrcs() {
  transport_ssrc_count = 0;
  memset(transport_ssrc, 0, sizeof(transport_ssrc));
}

void RtspStream::dissect_rtsp_conversation() {
  // 有client_port 与 server_port 的 建立与rtp的关联流
  if (transport_info_parsed_ == 1 || rtp_over_rtsp_parsed_ == 1) {
    // 添加状态检查，避免重复处理媒体映射
    if (transport_info_parsed_ == 1 && !media_mapping_created_) {
      sdp_.SetRtspinfo(this, transport_client_port_, transport_server_port_);
      media_mapping_created_ = 1;
    }
    if (rtp_over_rtsp_parsed_ == 1 && !media_mapping_created_) {
      sdp_.SetRtspinfo(this, NULL, NULL);
      media_mapping_created_ = 1;
    }

    // 在 TCP 模式下，如果解析到了 SSRC，需要建立 SSRC 到媒体类型的绑定
    if (rtp_over_rtsp_parsed_ == 1 && transport_ssrc_count > 0) {
      createSsrcMediaBinding();
    }
    int          sps_len = 0;
    char         sps[1024] = {0};
    int          pps_len = 0;
    char         pps[1024] = {0};
    ipc_decoder *decoder = static_cast<ipc_decoder *>(user);
    for (int i = 0; i < sdp_.info.m_info_num; i++) {
      for (int j = 0; j < sdp_.info.m_info[i].a_attr_num; j++) {
        char *sps_start = strstr(sdp_.info.m_info[i].a_attributes[j], "sprop-parameter-sets=");
        if (NULL != sps_start) {
          char *split = strchr(sps_start, ',');
          if (split) {
            sps_len = base64_decode(sps_start + 21, split - sps_start - 21, (unsigned char *)sps);
            pps_len = base64_decode(split + 1, strlen(split) - 1, (unsigned char *)pps);
          }
        }
      }
    }
    memset(sps_, 0, sizeof sps_);
    memset(pps_, 0, sizeof pps_);

    memcpy(sps_, sps, sps_len);
    sps_len_ = sps_len;

    memcpy(pps_, pps, pps_len);
    pps_len_ = pps_len;
    transport_info_parsed_ = 0;
    rtp_over_rtsp_parsed_ = 0;
    //
  }
}

int RtspStream::dissect_rtspmessage(const uint8_t *payload, const uint32_t payload_len) {
  int            first_linelen;
  const uint8_t *line;
  uint8_t        is_request_or_reply;
  rtsp_type_t    rtsp_type_packet;

  uint8_t body_requires_content_len;

  gpointer             ht_value = NULL;
  const uint8_t       *sdp_data = NULL;
  struct header_value *value;

  line = payload;
  first_linelen = find_packet_line_end(payload, payload_len);
  is_request_or_reply = is_rtsp_request_or_reply(line, first_linelen, &rtsp_type_packet);
  global_logger.Debug("dissect_rtspmessage ", payload);
  if (rtsp_type_packet != RTSP_NOT_FIRST_LINE) {
    body_requires_content_len = 1;
  } else {
    body_requires_content_len = 0;
    return -1;
  }

  /* 解析rtsp每行的 头部域，并将数据 插入hash表 */
  sdp_data = rtsp_parse_line_info(payload, payload_len);

  /*查看头部行是request 还是response         */
  //if (ht_value) {
  if (rstp_head_[EM_RTSP_H_HEADER].len > 0 && rstp_head_[EM_RTSP_H_HEADER].ptr != NULL) {
    char transfer[64] = {0};
    value = (struct header_value *)ht_value;
    memcpy(transfer, rstp_head_[EM_RTSP_H_HEADER].ptr,
        rstp_head_[EM_RTSP_H_HEADER].len >= sizeof(transfer) ? sizeof(transfer) - 1 : rstp_head_[EM_RTSP_H_HEADER].len);
    switch (rtsp_type_packet) {
      case RTSP_REQUEST:
        process_rtsp_request(rstp_head_[EM_RTSP_H_HEADER].ptr, rstp_head_[EM_RTSP_H_HEADER].len);
        break;

      case RTSP_REPLY:
        process_rtsp_reply(rstp_head_[EM_RTSP_H_HEADER].ptr, rstp_head_[EM_RTSP_H_HEADER].len);
        break;

      case RTSP_NOT_FIRST_LINE:
        /* Drop through, it may well be a header line */
        break;
      default:
        break;
    }
  }
  memset(&rstp_head_, 0, sizeof(rstp_head_));
  /* 解析sdp 数据*/
  if (sdp_data) {
    int            len_offset = sdp_data - payload;
    const uint32_t sdp_len = payload_len - len_offset;
    //printf("sdp  data_len:%d\n",sdp_len);
    dissect_rtsp_sdp(sdp_data, sdp_len, &sdp_info_);
    sdp_.dissect_sdp(this, 0, sdp_data, sdp_len);
  }
  dissect_rtsp_conversation();
  return 0;
}

int RtspStream::is_rtsp_message(const uint8_t *payload, int payload_len) {
  uint32_t       ii;
  const uint8_t *token, *next_token;
  int            tokenlen;
  char           response_chars[4];

  /* Is this an RTSP reply? */
  if (payload_len >= 5 && strncasecmp("RTSP/", (const char *)payload, 5) == 0) {
    return 1;
  }

  /*
     * Is this an RTSP request?
     * Check whether the line begins with one of the RTSP request
     * methods.
     */
  for (ii = 0; ii < RTSP_NMETHODS; ii++) {
    int len = strlen(rtsp_methods[ii]);
    if (payload_len >= len && strncasecmp(rtsp_methods[ii], (const char *)payload, len) == 0 &&
        (len == payload_len || isspace(payload[len]))) {
      return 1;
    }
  }
  return 0;
}

int RtspStream::is_rtp_over_rtsp(const uint8_t *payload, int payload_len) {
  if (payload[0] == RTSP_FRAMEHDR) { /* RTP/RTCP */
    return 1;
  }
  return 0;
}

int RtspStream::dissect_rtp_over_rtsp(const uint8_t *payload, const uint32_t payload_len) {
  int hl = 0;
  dissect_rtsp_conversation();
  RTSPInterleavedFrame *frame = (RTSPInterleavedFrame *)payload;
  hl = ntohs(frame->len);
  RTPKEEPER->dissectProto(this, 0, payload + sizeof(RTSPInterleavedFrame), payload_len - sizeof(RTSPInterleavedFrame));
  return 0;
}


// 返回 RTSP head 长度范围
// 负数: 没有找到
int64_t RtspStream::get_header_len(const uint8_t *p, int l) {
  if (0 == is_rtsp_message(p, l)) {
    return RTSP_ERROR;
  }

  const uint8_t *find = (const uint8_t *)memmem(p, l, "\x0D\x0A\x0D\x0A", 4);
  if (NULL == find) {
    return RTSP_NEED_MORE;
  }

  return find - p + 4;
}


// 返回 Content-Length 数值类型
int64_t RtspStream::get_contentlen(const uint8_t *p, int l) {
#define CONTENT_STR_LEN 16
  const char *str_start = "\r\nContent-Length";
  const char *str_end = "\r\n";

  if (l < CONTENT_STR_LEN)
    return 0;

  // 使用安全的不区分大小写字符串查找
  const char *find_start = safe_strncasestr((const char*)p, l, str_start);
  if (find_start) {
    find_start += CONTENT_STR_LEN;
    // 边界检查：确保find_start仍在有效范围内
    if (find_start >= (const char*)p + l)
      return 0;
  } else {
    return 0;
  }

  // 计算剩余长度进行边界检查
  size_t remaining_len = l - (find_start - (const char*)p);
  const char *find_end = safe_strncasestr(find_start, remaining_len, str_end);

  if (find_end == NULL || find_start + 15 < find_end)  //0xffffffff = 4294967295 = 4G
    return 0;

  int  i = 0;
  char buff[16];
  while (find_start < find_end && i < 15) {  // 添加缓冲区边界检查
    if (isdigit(*find_start))
      buff[i++] = *find_start;
    else if (*find_start != ':' && *find_start != ' ')
      return 0;
    find_start++;
  }
  buff[i] = 0;

  return atol(buff);
}

int RtspStream::get_rtsp_len(const uint8_t *payload, int payload_len, rtsp_type_t *type) {
  int64_t hl = 0;  // header  len
  int64_t cl = 0;  // content len
  int     offset = 0;
  offset = find_special_char(payload, payload_len, '$');
  if (offset >= 0) {
    *type = RTSP_RTP_OVER;
    RTSPInterleavedFrame *frame = (RTSPInterleavedFrame *)(payload + offset);
    hl = ntohs(frame->len) + sizeof(RTSPInterleavedFrame);
    if (hl > payload_len) {
      return RTSP_NEED_MORE;
    }
    if (*(payload + hl) == '$') {
      return hl;
    }
    return hl;
  }
  hl = get_header_len(payload, payload_len);
  if (RTSP_NEED_MORE == hl) {
    return RTSP_NEED_MORE;
  } else if (RTSP_ERROR == hl) {
    return RTSP_ERROR;
  }
  *type = RTSP_MESSAGE;
  cl = get_contentlen(payload, hl);
  if (cl > 0) {
    return hl + cl;
  }
  return hl;
}

int RtspStream::enqueRtspPayload(uint8_t C2S, const uint8_t *payload, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg) {
  //不考虑开头不为RTSP消息的报文 原因是 识别不出不会进入重组
  //进行应用层重组
  uint8_t    *p = (uint8_t *)payload;
  int         hl = 0;
  int         l = len;
  int         offset = 0;
  auto        c = cache + C2S;
  rtsp_type_t type = RTSP_UNKNOWN;

  // 如果缓存不为空，需要先处理缓存中的数据
  if (!c->empty()) {
    // 将新数据追加到缓存中
    std::copy(&payload[0], &payload[len], std::back_inserter(*c));
    p = c->data();
    l = c->size();
  }

  // 专业切割机 - 处理完整的RTSP消息
  while (offset < l) {
    hl = get_rtsp_len((const uint8_t *)p + offset, l - offset, &type);

    // 检查是否有完整的消息
    if (hl > 0 && l - offset >= hl) {
      // 处理完整的RTSP消息
      if (type == RTSP_MESSAGE) {
        // hl 已经包含了完整的RTSP消息长度（头部 + 内容）
        dissect_rtspmessage((const uint8_t *)p + offset, (uint32_t)hl);
      } else if (type == RTSP_RTP_OVER) {
        // hl 包含了完整的RTP over RTSP帧长度
        dissect_rtp_over_rtsp((const uint8_t *)p + offset, (uint32_t)hl);
      }
      offset += hl;
    } else if (hl == RTSP_ERROR) {
      // 解析错误，丢弃数据
      goto RTSP_DROP;
    } else if (hl == RTSP_NEED_MORE) {
      // 需要更多数据才能确定消息长度
      break;
    } else if (hl > l - offset) {
      // 消息长度超过当前可用数据，需要更多数据
      break;
    }
  }

  // 处理剩余数据
  if (offset >= 0 && offset < l) {
    if (!c->empty() && offset > 0) {
      // 已开启缓存，将已处理的数据从缓存中移除
      c->erase(c->begin(), c->begin() + offset);
    } else if (c->empty()) {
      // 未开启缓存，创建缓存保存剩余数据
      std::copy(p + offset, p + offset + (l - offset), std::back_inserter(*c));
    }
    goto RTSP_NEED_MORE_PKT;
  } else {
    // 所有数据都已处理完毕，清空缓存
    if (!c->empty()) {
      c->clear();
    }
    goto RTSP_NEED_MORE_PKT;
  }

  // RTSP解析错误，丢弃缓存数据
RTSP_DROP:
  if (!c->empty()) {
    c->clear();
  }

// RTSP解析需要更多报文
RTSP_NEED_MORE_PKT:
  return 0;
}
// ======================================      RtspKeeper   =======================================================
auto rtspkeeper = RTSPKEEPER;

RtspKeeper::RtspKeeper() {
  setRegCallback(IPC_PROTOCOL_RTSP, "rtsp",
      std::bind(&RtspKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&RtspKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&RtspKeeper::dissectProto_rsm, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4, std::placeholders::_5, std::placeholders::_6, std::placeholders::_7),
      std::bind(
          &RtspKeeper::miss, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
}

int RtspKeeper::dissectProto_rsm(
    void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg) {
  if (!(ptr && len))
    return -1;

  uint32_t                    offset = 0;
  flow_info                  *flow = static_cast<flow_info *>(user);
  std::shared_ptr<RtspStream> stream = nullptr;

  auto it = map_streams_.find(flow);
  if (it != map_streams_.end()) {
    stream = it->second;
  } else {
    auto newStreamPtr = std::make_shared<RtspStream>(flow);
    if (newStreamPtr == nullptr) {
      global_logger.Debug("std::make_shared<RtspStream>()");
      return -1;
    }
    stream = newStreamPtr;
    map_streams_[flow] = newStreamPtr;
  }
  stream->enqueRtspPayload(C2S, ptr, len, seq, ack, flg);
  return 0;
}
void RtspKeeper::dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  return;  //没重组的情况下不符合格式，暂时不写
}

void RtspKeeper::identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  int line_len = 0;

  line_len = find_packet_line_end(payload, payload_len);

  char buff[2000] ={0};
  if (line_len < 10|| line_len>sizeof(buff))
    return;
  int copy_len = payload_len>1999?1999:payload_len;
if(line_len - 9< 0 || line_len - 9 > copy_len ){
return ;
}
  memcpy(buff, payload, copy_len);
  if (strncasecmp((const char *)buff, "RTSP/1.0", 8) == 0 ||
      strncasecmp((const char *)buff + line_len - 9, " RTSP/1.0", 9) == 0) {
    flow->real_protocol_id = IPC_PROTOCOL_RTSP;
    printf("ip : %d.%d.%d.%d <-> %d.%d.%d.%d port : %d %d  proto: %d\n",
           flow->tuple.ip_src[0], flow->tuple.ip_src[1], flow->tuple.ip_src[2], flow->tuple.ip_src[3],
           flow->tuple.ip_dst[0], flow->tuple.ip_dst[1], flow->tuple.ip_dst[2], flow->tuple.ip_dst[3],
           ntohs(flow->tuple.port_src), ntohs(flow->tuple.port_dst), flow->tuple.proto);
    printf("buff %p : %s\n",buff, buff);
   printf("payload %p \n",payload);
    global_logger.Info("identifyProto = RTSP");
  }else
  if (strncasecmp((const char *)buff + line_len - 9, " RTSP/1.0", 9) == 0) {
    flow->real_protocol_id = IPC_PROTOCOL_RTSP;
    printf("ip : %d.%d.%d.%d <-> %d.%d.%d.%d port : %d %d  proto: %d\n",
           flow->tuple.ip_src[0], flow->tuple.ip_src[1], flow->tuple.ip_src[2], flow->tuple.ip_src[3],
           flow->tuple.ip_dst[0], flow->tuple.ip_dst[1], flow->tuple.ip_dst[2], flow->tuple.ip_dst[3],
           ntohs(flow->tuple.port_src), ntohs(flow->tuple.port_dst), flow->tuple.proto);
    printf("buff+%d-9 %p : %s\n",line_len,buff, buff + line_len - 9 );
   printf("payload %p payload_len %d\n",payload,payload_len);
    global_logger.Info("identifyProto = RTSP");
  }


  // if (payload[0] == RTSP_FRAMEHDR) { /* RTP/RTCP */
  //   flow->real_protocol_id = IPC_PROTOCOL_RTSP;
  //   global_logger.Info("identifyProto = RTSP");
  // }
  return;
}