
# module proto
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# add_library(proto_rtsp OBJECT
# slc_hdlc.cpp
# slc_rtp.cpp
# slc_sip.cpp
# slc_sdp.cpp
# slc_rtsp.cpp
# # slc_dhav.cpp
# )

# add_library(proto_dhav OBJECT
# slc_hdlc.cpp
# # slc_rtp.cpp
# # slc_sip.cpp
# # slc_sdp.cpp
# # slc_rtsp.cpp
# slc_dhav.cpp
# )

add_library(proto OBJECT
slc_hdlc.cpp
slc_rtp.cpp
slc_sip.cpp
slc_sdp.cpp
slc_rtsp.cpp
slc_dhav.cpp

)
