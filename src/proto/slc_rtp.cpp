#include "slc_rtp.h"
#include "ipc/ipc_decoder.h"
#include "slc_config.h"
#include "slc_logger.h"
#include "slc_detect.h"
#include "slc_rtsp.h"

extern slc::Logger global_logger;

auto rtpkeeper = RTPKEEPER;

int NaluCallback(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata) {
  RtpStream *stream = (RtpStream *)(userdata);
  if (stream->hasUserData()) {
    ipc_decoder *decoder = static_cast<ipc_decoder *>(stream->getUserData());

    decoder->onNaluCallback(naluType, nalu, naluLen, (void *)stream);
  } else {
    stream->onNaluCallback(naluType, nalu, naluLen, userdata);
  }

  return 0;
}

int H265NaluCallback(H265NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata) {
  RtpStream *stream = (RtpStream *)(userdata);
  if (stream->hasUserData()) {
    ipc_decoder *decoder = static_cast<ipc_decoder *>(stream->getUserData());

    // Convert H.265 NALU type to generic NALU type for decoder compatibility
    NaluType_e genericNaluType = NT_none;  // Default to none
    // Map some common H.265 types to H.264 equivalents for compatibility
    if (naluType == H265_NT_VPS || naluType == H265_NT_SPS) {
      genericNaluType = NT_SPS;
    } else if (naluType == H265_NT_PPS) {
      genericNaluType = NT_PPS;
    } else if (naluType == H265_NT_PREFIX_SEI || naluType == H265_NT_SUFFIX_SEI) {
      genericNaluType = NT_SEI;
    } else if (naluType >= H265_NT_TRAIL_N && naluType <= H265_NT_CRA_NUT) {
      // These are slice types, map to IDR or non-IDR based on type
      if (naluType >= H265_NT_BLA_W_LP && naluType <= H265_NT_CRA_NUT) {
        genericNaluType = NT_slice_IDR;  // Key frames
      } else {
        genericNaluType = NT_slice_nIDR; // Non-key frames
      }
    }

    decoder->onNaluCallback(genericNaluType, nalu, naluLen, (void *)stream);
  } else {
    stream->onH265NaluCallback(naluType, nalu, naluLen, userdata);
  }

  return 0;
}

RtpStream::RtpStream(RtpFlowKey &flowKey, flow_info *f_info) : H264Unpacker_(NaluCallback, this), H265Unpacker_(H265NaluCallback, this), flow_info(*f_info) {
  user_ = f_info->user;
  payload_type = flowKey.payload_type;
  ssrc = flowKey.ssrc;

  // 使用传入的参数直接调用getRtpMediaType，避免访问未完全构造的对象
  MediaType = RTPKEEPER->getRtpMediaType(f_info, flowKey.payload_type, flowKey.ssrc);
  global_logger.Info("creat RTP stream");
  //通过rtsp建立的RTP
  if ((f_info->real_protocol_id == IPC_PROTOCOL_RTSP || f_info->create_by == IPC_PROTOCOL_RTSP) && user_) {
    ipc_decoder *decoder = static_cast<ipc_decoder *>(user_);
    std::shared_ptr<RtspStream> stream = RTSPKEEPER->findRtspStreamByFlow(f_info);
    if (stream != NULL) {
      decoder->creatIpcStreamFromRTSP(stream.get(), this, ssrc, this->MediaType, (uint8_t *)stream->sps_, stream->sps_len_,
          (uint8_t *)stream->pps_, stream->pps_len_);
    }
  }
}

void RtpStream::enqueRtpStreamFragment(uint8_t *payload, uint32_t payload_len) {
  global_logger.Debug(" RtpStream::enqueRtpStreamFragment ",payload_len);

  if(MediaType == RtpMediaType::unknown && create_by == IPC_PROTOCOL_RTSP){
    MediaType = create_by_mediaType;
  }
  if (MediaType == RtpMediaType::video_h264)
    H264Unpacker_.enqueueRtpPayload(payload, payload_len);
  else if (MediaType == RtpMediaType::video_h265)
    H265Unpacker_.enqueueRtpPayload(payload, payload_len);
}

int RtpStream::onNaluCallback(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata) {
  NALU_t *n = new NALU_t;
  if (n == nullptr) {
    global_logger.Debug("new NALU_t bad new");
    return 0;
  }

  n->naluType = naluType;
  n->buffer.resize(naluLen);
  n->len = naluLen;
  std::copy(nalu, nalu + naluLen, n->buffer.begin());
  frameSeq_++;
  return 0;
}

int RtpStream::onH265NaluCallback(H265NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata) {
  NALU_t *n = new NALU_t;
  if (n == nullptr) {
    global_logger.Debug("new H265 NALU_t bad new");
    return 0;
  }

  // Convert H.265 NALU type to generic type for storage
  n->naluType = (uint8_t)naluType;  // Store the H.265 type directly
  n->buffer.resize(naluLen);
  n->len = naluLen;
  std::copy(nalu, nalu + naluLen, n->buffer.begin());
  frameSeq_++;
  return 0;
}

RtpKeeper::RtpKeeper() {
  setRegCallback(IPC_PROTOCOL_RTP, "rtp",
      std::bind(&RtpKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&RtpKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      NULL, NULL);
}

void RtpKeeper ::identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  // RTP流只能通过SIP或RTSP协议上下文创建，不再进行自动识别
  return;
}

void RtpKeeper ::dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  if (payload_len <= 12) {
    return;
  }
  uint16_t seq = get_uint16_ntohs(payload, 2);

  uint8_t  octet1 = get_uint8_t(payload, 0);
  uint8_t  rtp_version = CACL_RTP_VERSION(octet1);
  uint8_t  padding_set = RTP_PADDING(octet1);
  uint8_t  extension_set = RTP_EXTENSION(octet1);
  uint32_t csrc_count = RTP_CSRC_COUNT(octet1);

  uint8_t  octet2 = get_uint8_t(payload, 1);
  uint8_t  marker_set = RTP_MARKER(octet2);
  uint32_t payload_type = RTP_PAYLOAD_TYPE(octet2);

  uint16_t seq_num = get_uint16_ntohs(payload, 2);
  uint32_t timestamp = get_uint32_ntohl(payload, 4);
  uint32_t sync_src = get_uint32_ntohl(payload, 8);

  std::shared_ptr<RtpStream> stream = nullptr;

  RtpFlowKey rtpKey;
  rtpKey.port_dst = flow->tuple.port_dst;
  rtpKey.port_src = flow->tuple.port_src;
  rtpKey.payload_type = payload_type;
  rtpKey.ssrc = sync_src;
  auto it = map_streams_.find(rtpKey);
  if (it != map_streams_.end()) {
    stream = it->second;
  } else {
    RtpMediaType MediaType = getRtpMediaType(flow, payload_type, sync_src);
    if (MediaType == RtpMediaType::unknown) {
      return;
    }
    auto newStreamPtr = std::make_shared<RtpStream>(rtpKey, flow);
    if (newStreamPtr == nullptr) {
      global_logger.Debug("std::make_shared<RtpStream>()");
      return;
    }
    stream = newStreamPtr;
    newStreamPtr->payload_type = payload_type;
    map_streams_[rtpKey] = newStreamPtr;
  }
  global_logger.Debug("RTP:dissect s_port :", rtpKey.port_src," d_port:",rtpKey.port_dst," payload_type:",rtpKey.payload_type," ssrc:",rtpKey.ssrc);
  // 存储当前RTP时间戳
  stream->current_rtp_timestamp = timestamp;
  stream->enqueRtpStreamFragment((uint8_t *)payload + 12, payload_len - 12);
}

void RtpKeeper ::foreachRtpStreamer(std::function<int(RtpStream *)> func) {
  for (auto &pair : map_streams_) {
    func(pair.second.get());
  }

  return;
}

RtpMediaType RtpKeeper::getRtpMediaType(flow_info *flow, uint8_t payload_type, uint32_t ssrc) {
  printf("RtpKeeper::getRtpMediaType: payload_type=%d, ssrc=0x%x, src_port=%d, dst_port=%d\n",
         payload_type, ssrc, flow->tuple.port_src, flow->tuple.port_dst);

  // 首先尝试使用新的统一映射查找
  RtpMediaType unified_result = getUnifiedMediaType(flow, payload_type, ssrc);
  if (unified_result != RtpMediaType::unknown) {
    printf("RtpKeeper::getRtpMediaType: Found unified result=%d\n", (int)unified_result);
    return unified_result;
  }



  // 检查严格模式配置
  int strict_mode = RTP_STRICT_MODE;

  // 严格模式：只允许通过SSRC绑定的媒体类型
  if (strict_mode == 1) {
    global_logger.Debug("RTP严格模式：SSRC=0x%x 未找到绑定的媒体类型，拒绝端口绑定", ssrc);
    if(flow->create_by == IPC_PROTOCOL_RTSP){
      return flow->create_by_mediaType;
    }
    return RtpMediaType::unknown;
  }

  // 非严格模式：允许通过端口绑定查找媒体类型
  //sip关联的h264 - 通过端口绑定查找媒体类型
  RtpPortInfo rtpPort;
  rtpPort.port_dst = ntohs(flow->tuple.port_dst);
  rtpPort.port_src = ntohs(flow->tuple.port_src);
  rtpPort.payload_type = payload_type;

  // 首先尝试使用统一映射的端口查找 (情况3: SIP + UDP RTP)
  RtpMediaKey key_port = createKeyForSipUdp(ssrc, rtpPort.port_src, rtpPort.port_dst, rtpPort.payload_type);
  auto findUnifiedPortIter = map_unified_rtpMediaInfo_.find(key_port);
  if (findUnifiedPortIter == map_unified_rtpMediaInfo_.end()) {
    // 尝试反向端口
    key_port = createKeyForSipUdp(ssrc, rtpPort.port_dst, rtpPort.port_src, rtpPort.payload_type);
    findUnifiedPortIter = map_unified_rtpMediaInfo_.find(key_port);
  }

  // 如果使用实际SSRC没找到，尝试使用SSRC=0的映射（SIP协议中的临时映射）
  if (findUnifiedPortIter == map_unified_rtpMediaInfo_.end()) {
    key_port = createKeyForSipUdp(0, rtpPort.port_src, rtpPort.port_dst, rtpPort.payload_type);
    findUnifiedPortIter = map_unified_rtpMediaInfo_.find(key_port);
    if (findUnifiedPortIter == map_unified_rtpMediaInfo_.end()) {
      // 尝试反向端口
      key_port = createKeyForSipUdp(0, rtpPort.port_dst, rtpPort.port_src, rtpPort.payload_type);
      findUnifiedPortIter = map_unified_rtpMediaInfo_.find(key_port);
    }

    if (findUnifiedPortIter != map_unified_rtpMediaInfo_.end()) {
      printf("RTP非严格模式：通过SSRC=0的端口映射找到媒体类型，现在更新为实际SSRC=0x%x\n", ssrc);
      // 找到了SSRC=0的映射，现在用实际SSRC创建新的映射
      RtpMediaInfo minfo = findUnifiedPortIter->second;

      // 移除旧的SSRC=0映射
      map_unified_rtpMediaInfo_.erase(findUnifiedPortIter);
      // 同时移除可能的反向映射
      RtpMediaKey key_reverse = createKeyForSipUdp(0, rtpPort.port_dst, rtpPort.port_src, rtpPort.payload_type);
      map_unified_rtpMediaInfo_.erase(key_reverse);

      // 创建新的实际SSRC映射
      RtpMediaKey new_key = createKeyForSipUdp(ssrc, rtpPort.port_src, rtpPort.port_dst, rtpPort.payload_type);
      map_unified_rtpMediaInfo_[new_key] = minfo;

      if (flow->user) {
        auto decoder = static_cast<ipc_decoder *>(flow->user);
        decoder->creatIpcStreamFromSip(minfo.sipStream);
        decoder->creatIpcStreamFromRTSP(minfo.rtspStream, flow, ssrc, minfo.mediaType, NULL, 0, NULL, 0);
      }
      printf("RTP非严格模式：通过端口映射找到媒体类型并更新SSRC port_dst=%d, port_src=%d, payload_type=%d, ssrc=0x%x, mediaType=%d\n",
             rtpPort.port_dst, rtpPort.port_src, rtpPort.payload_type, ssrc, (int)minfo.mediaType);
      return minfo.mediaType;
    }
  }

  if (findUnifiedPortIter != map_unified_rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findUnifiedPortIter->second;
    if (flow->user) {
      auto decoder = static_cast<ipc_decoder *>(flow->user);
      decoder->creatIpcStreamFromSip(minfo.sipStream);
      decoder->creatIpcStreamFromRTSP(minfo.rtspStream, flow, ssrc, minfo.mediaType, NULL, 0, NULL, 0);
    }
    printf("RTP非严格模式：通过统一映射端口绑定找到媒体类型 port_dst=%d, port_src=%d, payload_type=%d, ssrc=0x%x, mediaType=%d\n",
           rtpPort.port_dst, rtpPort.port_src, rtpPort.payload_type, ssrc, (int)minfo.mediaType);
    return minfo.mediaType;
  }


  if(flow->create_by == IPC_PROTOCOL_RTSP){
    return flow->create_by_mediaType;
  }
  return RtpMediaType::unknown;
}
#if 0
RtpMediaType RtpKeeper::getRtpMediaType(RtpStream &stream) {
  guint8 type = stream.payload_type;

  switch (type) {
    case PT_PCMU:
    case PT_GSM:
    case PT_PCMA:  // g711
    case PT_G721:
    case PT_G723:
    case PT_G722:
    case PT_G728:
    case PT_G729:

      return RtpMediaType::audio;
    case PT_H261:
      return RtpMediaType::video_h261;
    case PT_H263:
      return RtpMediaType::video_h263;
  }

  // 首先尝试使用新的统一映射查找
  RtpMediaType unified_result = getUnifiedMediaType(stream);
  if (unified_result != RtpMediaType::unknown) {
    return unified_result;
  }



  // 检查严格模式配置
  int strict_mode = RTP_STRICT_MODE;

  // 严格模式：只允许通过SSRC绑定的媒体类型
  if (strict_mode == 1) {
    global_logger.Debug("RTP严格模式：SSRC=0x%x 未找到绑定的媒体类型，拒绝端口绑定", (uint32_t)stream.ssrc);
    return RtpMediaType::unknown;
  }

  // 非严格模式：允许通过端口绑定查找媒体类型
  //sip关联的h264 - 通过端口绑定查找媒体类型
  RtpPortInfo rtpPort;
  rtpPort.port_dst = ntohs(stream.tuple.port_dst);
  rtpPort.port_src = ntohs(stream.tuple.port_src);
  rtpPort.payload_type = stream.payload_type;

  // 首先尝试使用统一映射的端口查找 (情况3: SIP + UDP RTP)
  RtpMediaKey key_port = createKeyForSipUdp(stream.ssrc, rtpPort.port_src, rtpPort.port_dst, rtpPort.payload_type);
  auto findUnifiedPortIter = map_unified_rtpMediaInfo_.find(key_port);
  if (findUnifiedPortIter == map_unified_rtpMediaInfo_.end()) {
    // 尝试反向端口
    key_port = createKeyForSipUdp(stream.ssrc, rtpPort.port_dst, rtpPort.port_src, rtpPort.payload_type);
    findUnifiedPortIter = map_unified_rtpMediaInfo_.find(key_port);
  }

  if (findUnifiedPortIter != map_unified_rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findUnifiedPortIter->second;
    auto         decoder = static_cast<ipc_decoder *>(stream.user);
    decoder->creatIpcStreamFromSip(minfo.sipStream);
    printf("RTP Stream非严格模式：通过统一映射端口绑定找到媒体类型 port_dst=%d, port_src=%d, payload_type=%d, mediaType=%d\n",
           rtpPort.port_dst, rtpPort.port_src, rtpPort.payload_type, (int)minfo.mediaType);
    return minfo.mediaType;
  }



  return RtpMediaType::unknown;
}
#endif


// 新的统一映射方法实现
void RtpKeeper::addUnifiedMediaBinding(const RtpMediaKey& key, const RtpMediaInfo& minfo) {
  map_unified_rtpMediaInfo_[key] = minfo;
  printf("Added unified media binding: KeyType=%d, SSRC=0x%x, MediaType=%d\n",
         (int)key.key_type, key.ssrc, (int)minfo.mediaType);
}

// 辅助方法：根据不同情况创建RtpMediaKey
RtpMediaKey RtpKeeper::createKeyForRtspTcp(uint32_t ssrc) {
  return RtpMediaKey(ssrc);  // 情况1: 仅使用SSRC
}

RtpMediaKey RtpKeeper::createKeyForRtspUdp(uint32_t ssrc, uint32_t server_port) {
  return RtpMediaKey(ssrc, server_port, RtpMediaKey::SSRC_PORT_XOR);  // 情况2: SSRC与端口异或
}

RtpMediaKey RtpKeeper::createKeyForSipUdp(uint32_t ssrc, uint32_t port_src, uint32_t port_dst, uint8_t payload_type) {
  return RtpMediaKey(ssrc, port_src, port_dst, payload_type);  // 情况3: 完整信息
}

// 统一的媒体类型查找方法
RtpMediaType RtpKeeper::getUnifiedMediaType(flow_info *flow, uint8_t payload_type, uint32_t ssrc) {
  printf("RtpKeeper::getUnifiedMediaType: payload_type=%d, ssrc=0x%x, src_port=%d, dst_port=%d\n",
         payload_type, ssrc, flow ? flow->tuple.port_src : 0, flow ? flow->tuple.port_dst : 0);

  // 检查flow->user是否有效
  if (!flow || !flow->user) {
    printf("RTP统一查找失败：flow或flow->user为空\n");
    return RtpMediaType::unknown;
  }

  // 首先尝试情况1: RTSP + RTP over RTSP (仅SSRC)
  RtpMediaKey key1 = createKeyForRtspTcp(ssrc);
  auto findIter = map_unified_rtpMediaInfo_.find(key1);
  if (findIter != map_unified_rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findIter->second;
    auto decoder = static_cast<ipc_decoder *>(flow->user);
    decoder->creatIpcStreamFromSip(minfo.sipStream);
    decoder->creatIpcStreamFromRTSP(minfo.rtspStream, flow, ssrc, minfo.mediaType, NULL, 0, NULL, 0);
    printf("RTP统一查找成功(情况1-RTSP TCP)：SSRC=0x%x, MediaType=%d\n", ssrc, (int)minfo.mediaType);
    return minfo.mediaType;
  }

  // 尝试情况2: RTSP + UDP RTP (SSRC与端口异或)
  uint32_t flow_port = ntohs(flow->tuple.port_src);
  RtpMediaKey key2 = createKeyForRtspUdp(ssrc, flow_port);
  findIter = map_unified_rtpMediaInfo_.find(key2);
  if (findIter == map_unified_rtpMediaInfo_.end()) {
    // 尝试目标端口
    flow_port = ntohs(flow->tuple.port_dst);
    key2 = createKeyForRtspUdp(ssrc, flow_port);
    findIter = map_unified_rtpMediaInfo_.find(key2);
  }

  if (findIter != map_unified_rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findIter->second;
    if (flow->user) {
      auto decoder = static_cast<ipc_decoder *>(flow->user);
      decoder->creatIpcStreamFromSip(minfo.sipStream);
      decoder->creatIpcStreamFromRTSP(minfo.rtspStream, flow, ssrc, minfo.mediaType, NULL, 0, NULL, 0);
    }
    printf("RTP统一查找成功(情况2-RTSP UDP)：SSRC=0x%x, Port=0x%x, MediaType=%d\n",
           ssrc, flow_port, (int)minfo.mediaType);
    return minfo.mediaType;
  }

  // 尝试情况3: SIP + UDP RTP (完整信息)
  uint32_t port_src = ntohs(flow->tuple.port_src);
  uint32_t port_dst = ntohs(flow->tuple.port_dst);
  RtpMediaKey key3 = createKeyForSipUdp(ssrc, port_src, port_dst, payload_type);
  findIter = map_unified_rtpMediaInfo_.find(key3);
  if (findIter == map_unified_rtpMediaInfo_.end()) {
    // 尝试反向端口
    key3 = createKeyForSipUdp(ssrc, port_dst, port_src, payload_type);
    findIter = map_unified_rtpMediaInfo_.find(key3);
  }

  // 如果使用实际SSRC没找到，尝试使用SSRC=0的映射（SIP协议中的临时映射）
  if (findIter == map_unified_rtpMediaInfo_.end()) {
    key3 = createKeyForSipUdp(0, port_src, port_dst, payload_type);
    findIter = map_unified_rtpMediaInfo_.find(key3);
    if (findIter == map_unified_rtpMediaInfo_.end()) {
      // 尝试反向端口
      key3 = createKeyForSipUdp(0, port_dst, port_src, payload_type);
      findIter = map_unified_rtpMediaInfo_.find(key3);
    }

    if (findIter != map_unified_rtpMediaInfo_.end()) {
      printf("RTP统一查找：通过SSRC=0的端口映射找到媒体类型，现在更新为实际SSRC=0x%x\n", ssrc);
      // 找到了SSRC=0的映射，现在用实际SSRC创建新的映射
      RtpMediaInfo minfo = findIter->second;

      // 移除旧的SSRC=0映射
      map_unified_rtpMediaInfo_.erase(findIter);
      // 同时移除可能的反向映射
      RtpMediaKey key_reverse = createKeyForSipUdp(0, port_dst, port_src, payload_type);
      map_unified_rtpMediaInfo_.erase(key_reverse);

      // 创建新的实际SSRC映射
      RtpMediaKey new_key = createKeyForSipUdp(ssrc, port_src, port_dst, payload_type);
      map_unified_rtpMediaInfo_[new_key] = minfo;

      if (flow->user) {
        auto decoder = static_cast<ipc_decoder *>(flow->user);
        decoder->creatIpcStreamFromSip(minfo.sipStream);
        decoder->creatIpcStreamFromRTSP(minfo.rtspStream, flow, ssrc, minfo.mediaType, NULL, 0, NULL, 0);
      }
      printf("RTP统一查找成功(情况3-SIP UDP with SSRC update)：SSRC=0x%x, Port_src=%d, Port_dst=%d, PayloadType=%d, MediaType=%d\n",
             ssrc, port_src, port_dst, payload_type, (int)minfo.mediaType);
      return minfo.mediaType;
    }
  }

  if (findIter != map_unified_rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findIter->second;
    if (flow->user) {
      auto decoder = static_cast<ipc_decoder *>(flow->user);
      decoder->creatIpcStreamFromSip(minfo.sipStream);
      decoder->creatIpcStreamFromRTSP(minfo.rtspStream, flow, ssrc, minfo.mediaType, NULL, 0, NULL, 0);
    }
    printf("RTP统一查找成功(情况3-SIP UDP)：SSRC=0x%x, Port_src=%d, Port_dst=%d, PayloadType=%d, MediaType=%d\n",
           ssrc, port_src, port_dst, payload_type, (int)minfo.mediaType);
    return minfo.mediaType;
  }

  printf("RTP统一查找失败：SSRC=0x%x, PayloadType=%d\n", ssrc, payload_type);
  return RtpMediaType::unknown;
}

RtpMediaType RtpKeeper::getUnifiedMediaType(RtpStream &stream) {
  return getUnifiedMediaType(static_cast<flow_info*>(stream.getUserData()), stream.payload_type, (uint32_t)stream.ssrc);
}
