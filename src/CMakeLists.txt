# set(CMAKE_VERBOSE_MAKEFILE on)
set(warn_flags " -Wall -Wextra                  \
                -Wno-missing-field-initializers \
                -Wno-unused-parameter           \
                -Wno-unused-variable            \
                -Wno-unused-function            \
                -Wno-unused-but-set-variable    \
                -Wno-cast-qual")                # 暂不使用 -Waddress-of-packed-member 打包成员地址

if (GCC_VERSION VERSION_GREATER 7)
	  # gcc 版本大于 7.0 的编译参数
	  set(warn_flags "-Wno-format-truncation      \
                    -Wno-address-of-packed-member \
                    -std=c++17")
else()
	  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")
endif()

# set(CMAKE_C_FLAGS "-D_GNU_SOURCE -std=c11 ${CMAKE_C_FLAGS}  ${warn_flags} -fdiagnostics-color=always")
# set(CMAKE_CXX_FLAGS "-D_GNU_SOURCE ${CMAKE_CXX_FLAGS}  ${warn_flags}")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static-libasan")
set(CMAKE_C_FLAGS_ASAN "${CMAKE_C_FLAGS} -static-libasan -fsanitize=address -fno-omit-frame-pointer")
set(ASAN_FLAGS "-fno-omit-frame-pointer -fsanitize=address")
set(CMAKE_EXE_LINKER_FLAGS_ASAN "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static-libasan")


if(IS_ARM_OPENWRT)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -L${CMAKE_SOURCE_DIR}/lib/arm")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -L${CMAKE_SOURCE_DIR}/lib/amd64")
endif()



set(CMAKE_CXX_STANDARD 17)
message("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
message("CMAKE_SOURCE_DIR = " ${CMAKE_SOURCE_DIR})
include_directories(
  ${CMAKE_SOURCE_DIR}/include
  ${CMAKE_SOURCE_DIR}/src
  ${CMAKE_SOURCE_DIR}/src/framework
  ${CMAKE_SOURCE_DIR}/src/utils
  ${CMAKE_SOURCE_DIR}/src/input
  ${CMAKE_SOURCE_DIR}/src/codec
  ${CMAKE_SOURCE_DIR}/src/proto
  ${CMAKE_SOURCE_DIR}/src/output
  ${CMAKE_SOURCE_DIR}/test/xop
  ${CMAKE_SOURCE_DIR}/test/net
  ${CMAKE_SOURCE_DIR}/src/3rdpart/md5
  ${CMAKE_SOURCE_DIR}/include/glib-2.0/
  # /usr/include/glib-2.0/
  # /usr/lib64/glib-2.0/include
)


add_subdirectory(framework)
add_subdirectory(codec)
add_subdirectory(proto)
add_subdirectory(ipc)

# 创建主程序需要的最小framework库
add_library(framework_minimal OBJECT
  framework/plugin_loader.cpp
  framework/unix_socket.cpp
  framework/slc_config.cpp
)

add_executable(${AppName}
  slc_main.cpp
  slc_capture.cpp
  $<TARGET_OBJECTS:framework_minimal>
)

# 根据编译器类型选择链接目录
if(IS_ARM_OPENWRT)
    message("${AppName} target_link_directories ${CMAKE_SOURCE_DIR}/lib/arm")
    target_link_directories(${AppName} PRIVATE
        ${CMAKE_SOURCE_DIR}/lib/arm
    )
else()
    message("${AppName} target_link_directories ${CMAKE_SOURCE_DIR}/lib/amd64")
    target_link_directories(${AppName} PRIVATE
        ${CMAKE_SOURCE_DIR}/lib/amd64
    )
endif()

# 根据编译器类型选择链接库
if(IS_ARM_OPENWRT)
    target_link_libraries(${AppName}
        -Wl,--whole-archive
        iniparser
        -static-libstdc++
        pcap
        pthread
        tcp_rsm
        glib-2.0
        m
        iconv
        dl  # 添加dlopen支持
        -Wl,--no-whole-archive
    )
else()
    target_link_libraries(${AppName}
        -Wl,--whole-archive
        iniparser
        # -static-libstdc++
        pcap
        pthread
        tcp_rsm
        glib-2.0
        m
        # iconv
        dl  # 添加dlopen支持
        # -static-libasan

        # ${YA_PROJECT_LIBRARY}  # 移除静态链接，改为动态加载
        -Wl,--no-whole-archive
    )
endif()


set_target_properties(${AppName} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/run)



set(CMAKE_POSITION_INDEPENDENT_CODE ON)

add_library(${YA_PROJECT_LIBRARY} SHARED
$<TARGET_OBJECTS:framework_ipc>
$<TARGET_OBJECTS:codec>
$<TARGET_OBJECTS:proto>
$<TARGET_OBJECTS:ipc_decoder>
)



# 根据编译器类型选择共享库链接目录
if(IS_ARM_OPENWRT)
    message("${YA_PROJECT_LIBRARY} target_link_directories ${CMAKE_SOURCE_DIR}/lib/arm")
    target_link_directories(${YA_PROJECT_LIBRARY} PRIVATE
        ${CMAKE_SOURCE_DIR}/lib/arm
    )
else()
    message("${YA_PROJECT_LIBRARY} target_link_directories ${CMAKE_SOURCE_DIR}/lib/amd64")
    target_link_directories(${YA_PROJECT_LIBRARY} PRIVATE
        ${CMAKE_SOURCE_DIR}/lib/amd64
    )
endif()

# 根据编译器类型选择共享库链接库
if(IS_ARM_OPENWRT)
    target_link_libraries(${YA_PROJECT_LIBRARY}
        -Wl,--whole-archive
        iniparser
        -static-libstdc++
        pcap
        pthread
        tcp_rsm
        glib-2.0
        m
        iconv
        -Wl,--no-whole-archive
    )
else()
    target_link_libraries(${YA_PROJECT_LIBRARY}
        -Wl,--whole-archive
        iniparser
        # -static-libstdc++
        pcap
        pthread
        tcp_rsm
        glib-2.0
        m
        # iconv
        # -static-libasan
        -Wl,--no-whole-archive
    )
endif()
target_compile_options(${YA_PROJECT_LIBRARY} PUBLIC "-fPIC")
target_compile_options(${AppName} PUBLIC "-fPIC")
# target_link_libraries(${AppName}
# asan
# )

#target_link_libraries(${YA_PROJECT_LIBRARY}
#asan
#)

#  target_compile_options(${YA_PROJECT_LIBRARY} PUBLIC "-fsanitize=address")
#  target_link_options(${YA_PROJECT_LIBRARY} PUBLIC "-fsanitize=address")

#  target_compile_options(${AppName} PUBLIC "-fsanitize=address")
#  target_link_options(${AppName} PUBLIC "-fsanitize=address")

# target_compile_options(${AppName} PRIVATE -fsanitize=address -fno-omit-frame-pointer -L-static-libasan -g)
# target_link_options(${AppName} PRIVATE -fsanitize=address)

# target_compile_options(${YA_PROJECT_LIBRARY} PRIVATE -fsanitize=address -fno-omit-frame-pointer -L-static-libasan -g)
# target_link_options(${YA_PROJECT_LIBRARY} PRIVATE -fsanitize=address)

# 为可执行文件生成软链接
add_custom_command(TARGET ${YA_PROJECT_LIBRARY} POST_BUILD
  COMMAND ${CMAKE_COMMAND} -E create_symlink $<TARGET_FILE:${YA_PROJECT_LIBRARY}> ${CMAKE_SOURCE_DIR}/run/${YA_PROJECT_LIBRARY}
)
message("begin dpi_files = ${dpi_files}")
message("begin dpi_dirs = ${dpi_dirs}")