
#include <stdlib.h>
#include <memory.h>

#include "ipc_config.h"
#include "slc_detect.h"
#include "ipc_decoder.h"

void ipc_cfg_set_user_data(IPC_CONFIG *cfg_, void *userdata) {
  if (!cfg_) {
    return;
  }
  ipc_cfg *conf_p = static_cast<ipc_cfg *>(cfg_);
  conf_p->userdata = userdata;
}

void ipc_cfg_set_config_flag(IPC_CONFIG *cfg_, uint64_t flag) {
  if (!cfg_) {
    return;
  }
  ipc_cfg *conf_p = static_cast<ipc_cfg *>(cfg_);
  conf_p->flag = flag;
}

void ipc_cfg_set_config_frame_type(IPC_CONFIG *cfg_, enum IPC_FRAME_TYPE_E type) {
  if (!cfg_) {
    return;
  }
  ipc_cfg *conf_p = static_cast<ipc_cfg *>(cfg_);
  conf_p->type = type;
}

void ipc_cfg_set_config_log_level(IPC_CONFIG *cfg_, enum IPC_LOG_LEVEL_E level) {
  if (!cfg_) {
    return;
  }
  ipc_cfg *conf_p = static_cast<ipc_cfg *>(cfg_);
  conf_p->log_level = level;
}

void ipc_cfg_set_exclude_packet_ip(IPC_CONFIG *cfg_,uint32_t ip_hl){
  if (!cfg_) {
    return;
  }
  ipc_cfg *conf_p = static_cast<ipc_cfg *>(cfg_);
  conf_p->exclude_packet_ip = ip_hl;
}

extern "C" void ipc_cfg_set_tcp_rsm_out_of_order(IPC_CONFIG *cfg_, int window_size) {
  if (!cfg_) {
    return;
  }
  ipc_cfg *conf_p = static_cast<ipc_cfg *>(cfg_);
  conf_p->tcp_rsm_out_of_order = window_size;
}

uint32_t ipc_deocder_get_stream_src_ip(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);

  return *(const uint32_t *)info->tuple.ip_src;
}
uint32_t ipc_deocder_get_stream_dst_ip(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  return *(const uint32_t *)info->tuple.ip_dst;
}

uint16_t ipc_deocder_get_stream_src_port(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  return info->tuple.port_src;
}
uint16_t ipc_deocder_get_stream_dst_port(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  return info->tuple.port_dst;
}

uint8_t ipc_deocder_get_stream_transport_proto(IPC_DECODER *decoder_, uint64_t stream_id)  // [6 tcp]/[17 udp]
{
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  return info->tuple.proto;
}

size_t ipc_deocder_get_stream_count(IPC_DECODER *decoder_) {
  ipc_decoder *decoder = static_cast<ipc_decoder *>(decoder_);
  return decoder->getStreamCount();
}

uint32_t ipc_deocder_get_rtp_timestamp(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  if (info) {
    return info->current_rtp_timestamp;
  }
  return 0;
}
enum IPC_LOG_LEVEL_E  ipc_deocder_get_config_log_level(IPC_DECODER *decoder){
  ipc_decoder *decoder_ = static_cast<ipc_decoder *>(decoder);
  return (IPC_LOG_LEVEL_E )decoder_->getLogLevel();
}
/*******************************************************************************************************/
/*                 stream通过'ipc_deocder_get_stream_capability' 接口返回的能力获取信息              */
/*******************************************************************************************************/

uint64_t ipc_deocder_get_stream_capability(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  return info->capability;
}

enum IPC_AVC_E ipc_deocder_get_advanced_video_coding(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder *decoder = static_cast<ipc_decoder *>(decoder_);

  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  if ((info->capability & IPC_CAPABILITY_ADVANCED_VIDEO_CODING) == IPC_CAPABILITY_ADVANCED_VIDEO_CODING) {
    return info->capability_info.encodeType_video;
  }
  return IPC_AVC_NONE;
}

enum IPC_AAC_E ipc_deocder_get_advanced_audio_coding(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  if ((info->capability & IPC_CAPABILITY_ADVANCED_AUDIO_CODING) == IPC_CAPABILITY_ADVANCED_AUDIO_CODING) {
    return info->capability_info.encodeType_audio;
  }
  return IPC_AAC_NONE;
}

enum IPC_PROTOCOL_TYPE ipc_deocder_get_ipc_protocol(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  return info->capability_info.proto_type;
}

int ipc_deocder_get_stream_sample_rate(IPC_DECODER *decoder_, uint64_t stream_id) {
  ipc_decoder     *decoder = static_cast<ipc_decoder *>(decoder_);
  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  if ((info->capability & IPC_CAPABILITY_SAMPLE_RATE) == IPC_CAPABILITY_SAMPLE_RATE) {
    return info->capability_info.sample_rate;
  }
  return -1;
}

int ipc_deocder_get_deviceid(IPC_DECODER *decoder_, uint64_t stream_id, uint8_t *buff, size_t buff_len) {
  if (!buff || !decoder_) {
    return -1;
  }
  size_t copy_len = -1;

  ipc_decoder *decoder = static_cast<ipc_decoder *>(decoder_);

  ipc_stream_info *info = decoder->getStreamInfo(stream_id);
  if ((info->capability & IPC_CAPABILITY_DEVICEID) == IPC_CAPABILITY_DEVICEID) {
    copy_len = buff_len > strlen(info->capability_info.device_desc) ? strlen(info->capability_info.device_desc) : buff_len;
    memcpy(buff, info->capability_info.device_desc, copy_len);
  }
  return copy_len;
}

int ipc_deocder_get_stream_latest_sps(IPC_DECODER *decoder_, uint64_t stream_id, uint8_t *buff, size_t buff_len) {
  if (!buff || !decoder_) {
    return 0;
  }
  size_t copy_len = 0;

  ipc_decoder *decoder = static_cast<ipc_decoder *>(decoder_);

  ipc_stream_info *info = decoder->getStreamInfo(stream_id);

  if ((info->capability & IPC_CAPABILITY_SPS) == IPC_CAPABILITY_SPS) {
    copy_len = buff_len > info->capability_info.sps_len ? info->capability_info.sps_len : buff_len;
    memcpy(buff, info->capability_info.sps, copy_len);
  }

  return copy_len;
}

int ipc_deocder_get_stream_latest_pps(IPC_DECODER *decoder_, uint64_t stream_id, uint8_t *buff, size_t buff_len) {
  if (!buff || !decoder_) {
    return 0;
  }
  size_t copy_len = 0;

  ipc_decoder *decoder = static_cast<ipc_decoder *>(decoder_);

  ipc_stream_info *info = decoder->getStreamInfo(stream_id);

  if ((info->capability & IPC_CAPABILITY_PPS) == IPC_CAPABILITY_PPS) {
    copy_len = buff_len > info->capability_info.pps_len ? info->capability_info.pps_len : buff_len;
    memcpy(buff, info->capability_info.pps, copy_len);
  }
  return copy_len;
}

IPC_CONFIG *ipc_create_config(ipc_on_new_stream on_new_stream, ipc_on_new_nalu on_new_nalu) {
  ipc_cfg *cfg_p = new ipc_cfg;
  cfg_p->new_nalu_fn = on_new_nalu;
  cfg_p->new_stream_fn = on_new_stream;

  return static_cast<void *>(cfg_p);
}

IPC_DECODER *ipc_create_decoder(IPC_CONFIG *cfg_) {
  ipc_cfg *conf_p = static_cast<ipc_cfg *>(cfg_);

  ipc_decoder *decoder = new ipc_decoder(conf_p);

  if (!decoder)
    return NULL;

  return static_cast<void *>(decoder);
}

/**
 * @brief 销毁ipc_decoder句柄 同时也会自动销毁IPC_CFG内存
 * 
 */
void ipc_destroy_decoder(IPC_DECODER *decoder_) {
  ipc_decoder *decoder = static_cast<ipc_decoder *>(decoder_);
  delete decoder;
}

/*======================================================================================================
 * ipc_DECODER -  处理流量
 *=====================================================================================================*/

/**
 * @brief 调用此接口，需要在IPC_DECODER 初始化后调用
 *
 * @param buff 捕获的网络流量packet
 * @param len  捕获的网络流量packet的长度
 * @return int -1 为IPC_DECODER 未初始化 0为 失败 1为成功
 */
int ipc_process_packet(IPC_DECODER *decoder_, const uint8_t *buff, int len) {
  ipc_decoder *decoder = static_cast<ipc_decoder *>(decoder_);
  decoder->onProcessPkt(buff, len);
  return 0;
}
