#include "ipc/ipc.h"
#include "slc_detect.h"
#include "slc_rtp.h"

#ifndef __CODECS_IPC_STREAM_H__
#define __CODECS_IPC_STREAM_H__

struct ipc_capability_t {
  IPC_AAC_E         encodeType_audio = IPC_AAC_NONE;  // 编码类型；
  IPC_AVC_E         encodeType_video = IPC_AVC_NONE;  // 编码类型；
  int               sample_rate = 0;
  uint8_t           pps[1024] = {0};
  uint8_t           pps_len = 0;
  uint8_t           sps[1024] = {0};
  uint8_t           sps_len = 0;
  char              device_desc[1024] = {0};
  IPC_PROTOCOL_TYPE proto_type = IPC_PROTOCOL_UNKNOWN;  //协议识别后的应用层协议id
};

class ipc_stream_info : public flow_info {
public:
  ipc_stream_info(flow_info* f_info) : flow_info(*f_info) {
    if (f_info->real_protocol_id == IPC_PROTOCOL_RTP) {
    }
  }

  void setSPS(uint8_t* nalu, int naluLen) {
    capability_info.sps_len = naluLen;
    capability = capability | IPC_CAPABILITY_SPS;
    memset(capability_info.sps, 0, sizeof capability_info.sps);
    memcpy(capability_info.sps, nalu, naluLen);
  }

  void setPPS(uint8_t* nalu, int naluLen) {
    capability_info.pps_len = naluLen;
    capability = capability | IPC_CAPABILITY_PPS;
    memset(capability_info.pps, 0, sizeof capability_info.pps);
    memcpy(capability_info.pps, nalu, naluLen);
  }

  uint64_t                stream_id = 0;
  uint64_t                flag = 0;  // 接受的格式
  uint64_t                capability = 0;
  struct ipc_capability_t capability_info;
  uint64_t                seq = 0;
  uint32_t                current_rtp_timestamp = 0;  // 当前RTP时间戳
};
#endif