#ifndef RTP_H265_PACKER_H
#define RTP_H265_PACKER_H

#include "slc_nalu.h"

#include <vector>
#include <stdint.h>
#include <ctime>
#include "slc_typedefs.h"

#include "slc_logger.h"
extern slc::Logger global_logger;
#define MAX_NALU_SLICE_LEN 1360

// H.265/HEVC NALU types according to RFC 7798
enum H265NaluType_e {
  // VCL NAL unit types
  H265_NT_TRAIL_N = 0,      // Coded slice segment of a non-TSA, non-STSA trailing picture
  H265_NT_TRAIL_R = 1,      // Coded slice segment of a non-TSA, non-STSA trailing picture
  H265_NT_TSA_N = 2,        // Coded slice segment of a TSA picture
  H265_NT_TSA_R = 3,        // Coded slice segment of a TSA picture
  H265_NT_STSA_N = 4,       // Coded slice segment of an STSA picture
  H265_NT_STSA_R = 5,       // Coded slice segment of an STSA picture
  H265_NT_RADL_N = 6,       // Coded slice segment of a RADL picture
  H265_NT_RADL_R = 7,       // Coded slice segment of a RADL picture
  H265_NT_RASL_N = 8,       // Coded slice segment of a RASL picture
  H265_NT_RASL_R = 9,       // Coded slice segment of a RASL picture
  H265_NT_RSV_VCL_N10 = 10, // Reserved non-IRAP SLNR VCL NAL unit types
  H265_NT_RSV_VCL_R11 = 11, // Reserved non-IRAP sub-layer reference VCL NAL unit types
  H265_NT_RSV_VCL_N12 = 12, // Reserved non-IRAP SLNR VCL NAL unit types
  H265_NT_RSV_VCL_R13 = 13, // Reserved non-IRAP sub-layer reference VCL NAL unit types
  H265_NT_RSV_VCL_N14 = 14, // Reserved non-IRAP SLNR VCL NAL unit types
  H265_NT_RSV_VCL_R15 = 15, // Reserved non-IRAP sub-layer reference VCL NAL unit types
  H265_NT_BLA_W_LP = 16,    // Coded slice segment of a BLA picture
  H265_NT_BLA_W_RADL = 17,  // Coded slice segment of a BLA picture
  H265_NT_BLA_N_LP = 18,    // Coded slice segment of a BLA picture
  H265_NT_IDR_W_RADL = 19,  // Coded slice segment of an IDR picture
  H265_NT_IDR_N_LP = 20,    // Coded slice segment of an IDR picture
  H265_NT_CRA_NUT = 21,     // Coded slice segment of a CRA picture
  H265_NT_RSV_IRAP_VCL22 = 22, // Reserved IRAP VCL NAL unit types
  H265_NT_RSV_IRAP_VCL23 = 23, // Reserved IRAP VCL NAL unit types

  // Non-VCL NAL unit types
  H265_NT_VPS = 32,         // Video parameter set
  H265_NT_SPS = 33,         // Sequence parameter set
  H265_NT_PPS = 34,         // Picture parameter set
  H265_NT_AUD = 35,         // Access unit delimiter
  H265_NT_EOS = 36,         // End of sequence
  H265_NT_EOB = 37,         // End of bitstream
  H265_NT_FD = 38,          // Filler data
  H265_NT_PREFIX_SEI = 39,  // Supplemental enhancement information
  H265_NT_SUFFIX_SEI = 40,  // Supplemental enhancement information

  // RTP payload specific types (RFC 7798)
  H265_NT_AP = 48,          // Aggregation packet
  H265_NT_FU = 49,          // Fragmentation unit
  H265_NT_PACI = 50,        // PACI packet
};

typedef int (*onGotH265Nalu_callback_t)(H265NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata);

// rtp h265 解包器
// 输入为 rtp 负载，输出为 nalu(回调)
class RtpH265Unpacker {
public:
  RtpH265Unpacker(onGotH265Nalu_callback_t callback, void *userdata);

public:
  int enqueueRtpPayload(uint8_t *rtpPayload, int len);

private:
  int processAP(uint8_t *rtpPayload, int len);

  int processFU(uint8_t *rtpPayload, int len);

  // 重置FU状态，防止OOM
  void resetFuState();

  // 检查NALU缓冲区大小是否超限
  bool isNaluBufferOverLimit() const;

private:
  onGotH265Nalu_callback_t onGotH265Nalu_func_;
  std::vector<uint8_t> naluBuff_;  // 存放一个 nalu 的 buff;
  void                *userdata_;

  // FU状态管理，防止OOM
  enum FuState {
    FU_IDLE,      // 空闲状态，等待start
    FU_RECEIVING  // 正在接收FU片段
  } fuState_;

  uint16_t lastRtpSeq_;           // 上一个RTP包的序列号
  bool     hasValidRtpSeq_;       // 是否有有效的RTP序列号

  // 安全限制
  static const size_t MAX_NALU_SIZE = 2 * 1024 * 1024;  // 2MB最大NALU大小
  static const size_t MAX_FU_FRAGMENTS = 4000;          // 最大FU片段数量
  size_t fuFragmentCount_;        // 当前FU片段计数
};

#endif /* RTP_H265_PACKER_H */
