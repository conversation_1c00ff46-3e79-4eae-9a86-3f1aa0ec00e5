#include "rtp_h265_packer.h"
#include <algorithm>
#include <arpa/inet.h>
extern slc::Logger global_logger;

struct H265NaluHeader {
  H265NaluHeader(uint16_t naluHeader) : data(naluHeader) {}

  H265NaluType_e getType() {
    return (H265NaluType_e)((data >> 9) & 0x3f);  // 取第9-14位，6位NALU类型
  }

  uint8_t getLayerId() { 
    return (data >> 3) & 0x3f;  // 取第3-8位，6位layer_id
  }

  uint8_t getTid() { 
    return data & 0x07;  // 取第0-2位，3位temporal_id_plus1
  }

  uint16_t data;
};

struct H265FuHeader {
  H265FuHeader(uint8_t fuHeader) : data(fuHeader) {}

  enum FU_type {
    FU_ill,    // 错误 fu
    FU_start,
    FU_middle,
    FU_end,
  };

  FU_type getType() {
    // S bit (start) 是第7位
    if ((data & 0x80) == 0x80) {
      return FU_start;
    }

    // E bit (end) 是第6位
    if ((data & 0x40) == 0x40) {
      return FU_end;
    }

    // 既不是start也不是end，就是middle
    if ((data & 0xc0) == 0) {
      return FU_middle;
    }

    return FU_ill;
  }

  H265NaluType_e getFuType() { 
    return (H265NaluType_e)(data & 0x3f);  // 取第0-5位，6位FuType
  }

  uint8_t data;
};

RtpH265Unpacker::RtpH265Unpacker(onGotH265Nalu_callback_t callback, void *userdata)
    : onGotH265Nalu_func_(callback), userdata_(userdata), fuState_(FU_IDLE),
      lastRtpSeq_(0), hasValidRtpSeq_(false), fuFragmentCount_(0) {
  naluBuff_.reserve(1400);
}

// H.265 Aggregation Packet (AP) 处理
// | 2 byte   | 2 byte     | n byte | 2 byte     | n byte |
// nalu-header nalu-size-01 nalu-01 nalu-size-02 nalu-02 ...
int RtpH265Unpacker::processAP(uint8_t *rtpPayload, int len) {
    global_logger.Debug("RtpH265Unpacker::processAP ", len );

  for (int offset = 2; offset < len;) {  // H.265 NALU header是2字节
    // get nalu size and skip it.
    uint16_t *pNaluSize = (uint16_t *)&rtpPayload[offset];
    uint16_t  naluSize = ntohs(*pNaluSize);
    offset += 2;

    // 检查边界
    if (offset + naluSize > len) {
      global_logger.Error("H.265 AP: NALU size exceeds payload boundary");
      return -1;
    }

    // one nalu
    H265NaluHeader header(ntohs(*(uint16_t*)&rtpPayload[offset]));
    onGotH265Nalu_func_(header.getType(), &rtpPayload[offset], naluSize, userdata_);

    // forward to next (size + nalu)
    offset += naluSize;
  }

  return 0;
}

int RtpH265Unpacker::processFU(uint8_t *rtpPayload, int len) {
  if (len < 3) {  // H.265 FU至少需要3字节：2字节NALU header + 1字节FU header
    global_logger.Error("H.265 FU payload too short");
    return -1;
  }

  H265FuHeader fuHeader(rtpPayload[2]);  // FU header在第3字节
  H265FuHeader::FU_type fuType = fuHeader.getType();

  // 检查缓冲区是否超限，如果超限则重置状态
  if (isNaluBufferOverLimit()) {
    global_logger.Error("H.265 NALU buffer over limit, resetting FU state");
    resetFuState();
    return -1;
  }

  if (fuType == H265FuHeader::FU_start) {
    // 重置状态，开始新的NALU
    resetFuState();
    fuState_ = FU_RECEIVING;
    global_logger.Debug("fuType == H265FuHeader::FU_start " ,len);

    // 构造NALU header：使用原始NALU header的前2字节，但替换NALU类型
    uint16_t originalHeader = ntohs(*(uint16_t*)rtpPayload);
    uint16_t newHeader = (originalHeader & 0x81FF) | ((fuHeader.getFuType() & 0x3f) << 9);
    
    // 添加重构的NALU header
    naluBuff_.push_back((newHeader >> 8) & 0xff);
    naluBuff_.push_back(newHeader & 0xff);
    
    // 3字节后数据
    std::copy(&rtpPayload[3], &rtpPayload[len], std::back_inserter(naluBuff_));
    fuFragmentCount_++;

  } else if (fuType == H265FuHeader::FU_middle) {

    global_logger.Debug("fuType == H265FuHeader::FU_middle ",len);
    // 只有在RECEIVING状态才接受middle片段
    if (fuState_ != FU_RECEIVING) {
      global_logger.Warning("H.265 FU middle received but not in RECEIVING state, ignoring");
      return -1;
    }

    // 3字节后数据
    std::copy(&rtpPayload[3], &rtpPayload[len], std::back_inserter(naluBuff_));
    fuFragmentCount_++;

  } else if (fuType == H265FuHeader::FU_end) {
    // 只有在RECEIVING状态才接受end片段
    global_logger.Debug("fuType == H265FuHeader::FU_end ",len);
    if (fuState_ != FU_RECEIVING) {
      global_logger.Warning("H.265 FU end received but not in RECEIVING state, ignoring");
      return -1;
    }
    
    // 3字节后数据
    std::copy(&rtpPayload[3], &rtpPayload[len], std::back_inserter(naluBuff_));
    fuFragmentCount_++;

    // 递交 nalu
    if (!naluBuff_.empty()) {
      H265NaluHeader header(ntohs(*(uint16_t*)naluBuff_.data()));
      onGotH265Nalu_func_(fuHeader.getFuType(), naluBuff_.data(), naluBuff_.size(), userdata_);
    }

    // 重置状态
    resetFuState();

  } else {
    global_logger.Error("Invalid H.265 FU type received");
    return -1;
  }

  return 0;
}

void RtpH265Unpacker::resetFuState() {
  fuState_ = FU_IDLE;
  fuFragmentCount_ = 0;
  naluBuff_.clear();
global_logger.Debug("resetFuState");

}

bool RtpH265Unpacker::isNaluBufferOverLimit() const {
  return naluBuff_.size() > MAX_NALU_SIZE || fuFragmentCount_ > MAX_FU_FRAGMENTS;
}

int RtpH265Unpacker::enqueueRtpPayload(uint8_t *rtpPayload, int len) {
  if (len < 2) {
    global_logger.Error("H.265 RTP payload too short");
    return -1;
  }

  H265NaluHeader header(ntohs(*(uint16_t*)rtpPayload));
  global_logger.Debug("H.265 enqueueRtpPayload ",len);

  H265NaluType_e naluType = header.getType();

  // 处理单个NALU单元
  if (naluType <= H265_NT_RSV_IRAP_VCL23 || 
      (naluType >= H265_NT_VPS && naluType <= H265_NT_SUFFIX_SEI)) {
    onGotH265Nalu_func_(naluType, rtpPayload, len, userdata_);
  } else if (naluType == H265_NT_AP) {  // Aggregation Packet
    processAP(rtpPayload, len);
  } else if (naluType == H265_NT_FU) {  // Fragmentation Unit
    processFU(rtpPayload, len);
  } else {
    global_logger.Warning("Unknown H.265 NALU type: %d", naluType);
  }

  return 0;
}
