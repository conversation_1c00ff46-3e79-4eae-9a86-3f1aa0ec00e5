#ifndef __SLC_DETECT_H
#define __SLC_DETECT_H
#include "ipc/ipc.h"
#include "slc_flow.h"
#include "ipc/ipc_decoder.h"
#include "slc_logger.h"
#include "slc_typedefs.h"
#include "slc_tcp.h"
#include "slc_observer.h"

#include "tcp_rsm/tcp_rsm.h"

#ifndef ETH_P_IP
#define ETH_P_IP 0x0800 /* IPv4 */
#endif

#ifndef GRE_WCCP
#define GRE_WCCP 0x883E
#endif

#ifndef ETH_P_IPV6
#define ETH_P_IPV6 0x86dd /* IPv6 */
#endif

#define CISCO_D_PROTO 0x2000 /* Cisco Discovery Protocol */

#define VLAN 0x8100
#define MPLS_UNI 0x8847
#define MPLS_MULTI 0x8848
#define MPLS_PWETHCW 0x0000
#define PPPoE 0x8864            //会话报文
#define PPPoE_DISCOVERY 0x8863  //发现报文
#define SNAP 0xaa
#define BSTP 0x42 /* Bridge Spanning Tree Protocol */

#define VMLAB 0x88de
#define VMLAB_SIZE 24
#define VMLAB_ENCPTYPE_OFFSET 22

//huangzw
#define ARP 0x0806
#define LACP 0x8809
#define LLDP 0x88CC

#define PPP_IP 0x0021  //IP报文
#define PPP_IPCP 0x8021
#define PPP_LCP 0xc021
#define PPP_PAP 0xc023
#define PPP_CHAP 0xc223

enum pkt_status {
  PKT_OK,
  PKT_STOLEN,
  PKT_REASSEMBLE,
  PKT_DROP
};

extern slc::Logger global_logger;

class ProtoSubject {
public:
  // 有识别出type的，可以给解析
  void notifyProtoKeeper(uint32_t msgtype, flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
    std::lock_guard<std::mutex> lck(MapMtx);

    auto g_RegDissectCallbackMap = getRegDissectCallbackMap();

    auto it = g_RegDissectCallbackMap.find(msgtype);
    if (it == g_RegDissectCallbackMap.end()) {
      global_logger.Warning("Process Module Is Not Register . ProtoID={}", msgtype);
      return;
    }
    if (g_RegDissectCallbackMap[msgtype].dissect_rsm_func) {
      flow_tcp_rsm(flow, flow->tcph, payload, payload_len);
    }
    if (g_RegDissectCallbackMap[msgtype].dissect_func) {
      g_RegDissectCallbackMap[msgtype].dissect_func(flow, C2S, payload, payload_len);
    }
  }

  //没识别出协议的，先识别协议
  void notifyProtoKeeper(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
    std::lock_guard<std::mutex> lck(MapMtx);

    auto g_RegDissectCallbackMap = getRegDissectCallbackMap();

    for (auto it : g_RegDissectCallbackMap) {
      it.second.identify_func(flow, C2S, payload, payload_len);
    }
  }

private:
  std::mutex MapMtx;
};

#define SLCPARSER SlcParser::GetInstance()
/**
 * @brief 公共解析层 作为一个单例存在 每个decoder调用该单例进行解析应用层前的部分
 * 
 */
class SlcParser : ProtoSubject {
public:
  static SlcParser *GetInstance();

public:
  int parser(const uint8_t *pPktData, int len, void *);

private:
  int parserIpLayer(struct pkt_info *pkt, const void *ip4_or_ip6, uint16_t ipsize, void *user);

  flow_info *findCreateFlow(five_tuple *key);

  void destoryFlow(five_tuple *key);

  void onIdentifyProto();

  void onIdentifyProto(slc_flow_info_t *flow_info, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);
  void onDissectProto(slc_flow_info_t *flow_info, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);

private:
  std::unordered_map<Node<std::string, std::string, uint16_t, uint16_t, uint8_t, uint8_t>, flow_info, hash_func> flow_map;
};

#endif
