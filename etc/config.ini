MODE = 0                                        # 0: offline pcap, 1: capture on interface
IF = ens33                                     # 捕获网卡名
PCAP_FILES_DIR = /home/<USER>/pcap/H265                  # pcap files dir
#RTXDR_CAP_FILTER = "host ***************"      #过滤器
ARM_DEVICE = 0                                  #1 = arm64 ;0 = amd64
RTP_TRANSFER_MODE = 0                          #1 = 原样转发 ; 0 = 解析nalu转发
LOG_LEVEL = 0                         #log等级 0 Debug  1 Error 2 Warning 3 INFO
HEARTBEAT_INTERVAL = 5                         # 心跳间隔(秒), 默认5秒
CAPTURE_PATH = /root/pcaps/                    # 抓包文件保存路径
CAPTURE_FILE_SIZE = 100                        # 抓包文件大小限制(MB)
DEFAULT_BRAND = Unknown                        # 默认品牌名
RTP_STRICT_MODE = 0                            # RTP严格模式: 0=非严格模式(允许端口绑定), 1=严格模式(仅允许SSRC绑定)
DEFAULT_MODEL = Unknown                        # 默认型号
MAX_MESSAGE_SIZE = 512                         # 最大nalu大小(kb)
TCP_RSM_OUT_OF_ORDER = 5                       # TCP重组乱序窗口大小，默认5
save_nalu = 0                                  # 测试功能，保存视频文件到当前目录下
send_audio = 1                                 # 是否发送音频流
