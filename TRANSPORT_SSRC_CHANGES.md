# Transport SSRC 数组修改说明

## 修改概述

将 RTSP 流中的 `transport_ssrc` 从单个值修改为数组，以支持一个 RTSP 流中存在多个 SSRC。

## 修改的文件

### 1. src/proto/slc_rtsp.h

#### 修改内容：
- 在 `RtspStream` 类中添加了 `transport_ssrc_count` 成员变量来记录当前存储的 SSRC 数量
- 修改构造函数，初始化 `transport_ssrc_count` 为 0 并清空 `transport_ssrc` 数组
- 添加了三个辅助函数的声明：
  - `getSsrc(int index = 0)`: 获取指定索引的 SSRC，默认获取第一个
  - `getSsrcCount()`: 获取 SSRC 数量
  - `clearSsrcs()`: 清空 SSRC 数组
- 添加了必要的头文件包含：`<vector>`, `<memory>`, `<cstring>`

#### 具体修改：
```cpp
// 原来：
uint32_t transport_ssrc[12];

// 修改后：
uint32_t transport_ssrc[12];
int      transport_ssrc_count = 0;  // 记录当前存储的SSRC数量

// 构造函数修改：
RtspStream(flow_info *f_info) : flow_info(*f_info) {
  transport_ssrc_count = 0;
  memset(transport_ssrc, 0, sizeof(transport_ssrc));
}
```

### 2. src/proto/slc_rtsp.cpp

#### 修改内容：

1. **修改 `process_rtsp_reply_transport` 函数**：
   - 将单个 SSRC 解析改为支持多个 SSRC
   - 添加重复 SSRC 检查，避免重复添加
   - 为兼容性保留 `sdp_.ssrc` 的赋值（使用第一个 SSRC）

2. **修改 `createSsrcMediaBinding` 函数**：
   - 支持为多个媒体流分配对应的 SSRC
   - 如果 SSRC 数量不够，使用第一个 SSRC 作为后备
   - 移除了只处理第一个媒体流的限制

3. **修改 TCP 模式下的 SSRC 检查**：
   - 将 `ssrc != 0` 改为 `transport_ssrc_count > 0`

4. **添加辅助函数实现**：
   - `getSsrc()`: 安全地获取指定索引的 SSRC
   - `getSsrcCount()`: 返回当前 SSRC 数量
   - `clearSsrcs()`: 清空 SSRC 数组

#### 关键修改示例：

```cpp
// SSRC 解析逻辑修改：
if (strncmp(token, marker_ssrc, sizeof marker_ssrc - 1) == 0) {
  char *ssrc_str = token + sizeof(marker_ssrc) - 1;
  uint32_t parsed_ssrc = (uint32_t)strtoul(ssrc_str, NULL, 16);
  
  // 检查是否已经存在该SSRC，避免重复添加
  bool ssrc_exists = false;
  for (int i = 0; i < transport_ssrc_count; i++) {
    if (transport_ssrc[i] == parsed_ssrc) {
      ssrc_exists = true;
      break;
    }
  }
  
  // 如果SSRC不存在且还有空间，则添加到数组中
  if (!ssrc_exists && transport_ssrc_count < 12) {
    transport_ssrc[transport_ssrc_count] = parsed_ssrc;
    transport_ssrc_count++;
    printf("Parsed SSRC from Transport header: 0x%x (count: %d)\n", parsed_ssrc, transport_ssrc_count);
  }
  
  // 为了兼容性，将第一个SSRC赋值给sdp_.ssrc
  if (transport_ssrc_count > 0) {
    sdp_.ssrc = transport_ssrc[0];
  }
}
```

## 功能特性

1. **多 SSRC 支持**：可以存储最多 12 个 SSRC
2. **重复检查**：避免重复添加相同的 SSRC
3. **向后兼容**：保持 `sdp_.ssrc` 的使用，使用第一个 SSRC 作为默认值
4. **安全访问**：提供边界检查的 SSRC 访问函数
5. **媒体流映射**：支持为多个媒体流分配对应的 SSRC

## 使用方式

```cpp
RtspStream stream(&flow_info);

// 获取 SSRC 数量
int count = stream.getSsrcCount();

// 获取指定索引的 SSRC
uint32_t first_ssrc = stream.getSsrc(0);  // 第一个 SSRC
uint32_t second_ssrc = stream.getSsrc(1); // 第二个 SSRC

// 清空所有 SSRC
stream.clearSsrcs();
```

## 注意事项

1. 数组大小限制为 12 个 SSRC
2. 保持了与现有代码的兼容性
3. 在 TCP 模式下，每个媒体流可以有自己的 SSRC
4. 如果媒体流数量超过 SSRC 数量，会使用第一个 SSRC 作为后备

## 编译状态

修改后的代码已成功编译通过，没有编译错误或警告。
