cmake_minimum_required(VERSION 3.14)

# project
project(http_server LANGUAGES C CXX VERSION 0.0.1)
option(USE_PKG_CONFIG_TO_LINK off)

# set(CMAKE_C_STANDARD 17)

#
# ftyepsTest
#
include_directories(
  ${CMAKE_SOURCE_DIR}/include)

add_executable(http_server
  main.c
  mongoose/mongoose.c
  cjson/cJSON.c
  cjson/cJSON_Utils.c
)
set(CMAKE_C_FLAGS "-I./cjson -I./mongoose -g -Wall -Wno-unused-variable")


# 根据编译器类型选择共享库链接目录
if(IS_ARM_OPENWRT)
message("http_server target_link_directories ${CMAKE_SOURCE_DIR}/lib/arm")
link_directories(CMAKE_SOURCE_DIR/lib/arm) 
target_link_directories(${YA_PROJECT_LIBRARY} PRIVATE
${CMAKE_SOURCE_DIR}/lib/arm
)
else()
message("http_server target_link_directories ${CMAKE_SOURCE_DIR}/lib/amd64")
link_directories(CMAKE_SOURCE_DIR/lib/amd64) 
    target_link_directories(${YA_PROJECT_LIBRARY} PRIVATE
        ${CMAKE_SOURCE_DIR}/lib/amd64
    )
endif()


target_link_libraries(http_server
  iniparser
)
