情况1:
流A： TCP : RTSP + RTPoverRTSP
  sdp中存在 `c=IN IP4 ***********`
  sdp中存在 `m= vedio 0 RTP/AVP payloadType_video`
  sdp中存在 `m= audio 0 RTP/AVP payloadType_audio`
  sdp中存在 `a= rtpmap:payloadType_video MediaType_video/RATE`
  sdp中存在 `a= rtpmap:payloadType_audio MediaType_audio/RATE`
  transport中存在ssrc 
  transport中存在 interleaved
  本条流中的rtp payload_type 与 sdp中 payloadType_video 或 payloadType_audio 相同
  本条流的端口与sdp中port无关
  样例
  ```
  OPTIONS rtsp://************ RTSP/1.0
  CSeq: 1
  User-Agent: HIKVISION player NVR V4.73.006

  RTSP/1.0 200 OK
  CSeq: 1
  Public: OPTIONS,DESCRIBE,SETUP,PLAY,PAUSE,TEARDOWN,ANNOUNCE,SET_PARAMETER,GET_PARAMETER

  DESCRIBE rtsp://************ RTSP/1.0
  CSeq: 2
  Accept: application/sdp
  User-Agent: HIKVISION player NVR V4.73.006

  RTSP/1.0 401 ClientUnAuthorized
  CSeq: 2
  WWW-Authenticate: Digest realm="c479053c07ae",nonce="b86d277336e0004243a85cabad23ee8c", stale="FALSE"

  DESCRIBE rtsp://************ RTSP/1.0
  CSeq: 3
  Accept: application/sdp
  Authorization: Digest username="admin", realm="c479053c07ae", nonce="b86d277336e0004243a85cabad23ee8c", uri="/", response="4ae180180306b3bb3b6effac822971a0"
  User-Agent: HIKVISION player NVR V4.73.006

  RTSP/1.0 200 OK
  CSeq: 3
  Content-Base: rtsp://************
  Content-Length: 621
  Content-Type: application/sdp

  v=0
  o=- 1001 1 IN IP4 ************
  s=VCP IPC Realtime stream
  m=video 0 RTP/AVP 108
  c=IN IP4 ************
  a=control:rtsp://************/media/video1/video
  a=rtpmap:108 H265/90000
  a=fmtp:108 sprop-sps=QgEBAWAAAAMAAAMAAAMAAAMAlqABICAFEWNrkkya5ZwCAAADAAIAAAMAMhA=; sprop-pps=RAHgdrAmQA==
  a=recvonly
  m=audio 0 RTP/AVP 0
  c=IN IP4 ************
  a=fmtp:0 RTCP=0
  a=control:rtsp://************/media/video1/audio1
  a=recvonly
  m=application 0 RTP/AVP 107
  c=IN IP4 ************
  a=control:rtsp://************/media/video1/metadata
  a=rtpmap:107 vnd.onvif.metadata/90000
  a=fmtp:107 DecoderTag=h3c-v3 RTCP=0
  a=recvonly
  SETUP rtsp://************/media/video1/video RTSP/1.0
  CSeq: 4
  Transport: RTP/AVP/TCP;unicast;interleaved=0-1
  Authorization: Digest username="admin", realm="c479053c07ae", nonce="b86d277336e0004243a85cabad23ee8c", uri="/", response="73eddb639d672a8bb23bac77212273c5"
  User-Agent: HIKVISION player NVR V4.73.006

  RTSP/1.0 200 OK
  CSeq: 4
  Transport: RTP/AVP/TCP;unicast;interleaved=0-1;ssrc=811ce7a;mode="PLAY"
  Session: 755b1e02585b1e02755b1e027d6b1e0;timeout=60

  SETUP rtsp://************/media/video1/audio1 RTSP/1.0
  CSeq: 5
  Transport: RTP/AVP/TCP;unicast;interleaved=2-3
  Session: 755b1e02585b1e02755b1e027d6b1e0
  Authorization: Digest username="admin", realm="c479053c07ae", nonce="b86d277336e0004243a85cabad23ee8c", uri="/", response="73eddb639d672a8bb23bac77212273c5"
  User-Agent: HIKVISION player NVR V4.73.006

  RTSP/1.0 200 OK
  CSeq: 5
  Transport: RTP/AVP/TCP;unicast;interleaved=2-3;ssrc=5c5c3fa;mode="PLAY"
  Session: 755b1e02585b1e02755b1e027d6b1e0;timeout=60
  ```
情况2:
流A:  tcp : RTSP  
  transport中存在ssrc 
  transport中存在 client_port 与 server_port
  sdp中存在 `m= vedio 0 RTP/AVP payloadType_video`
  sdp中存在 `m= audio 0 RTP/AVP payloadType_audio`
  sdp中存在 `a= rtpmap:payloadType_video MediaType_video/RATE`
  sdp中存在 `a= rtpmap:payloadType_audio MediaType_audio/RATE`
  样例
  ```
  DESCRIBE rtsp://**************:8554/stream RTSP/1.0
  CSeq: 3
  User-Agent: LibVLC/3.0.18 (LIVE555 Streaming Media v2016.11.28)
  Accept: application/sdp

  RTSP/1.0 200 OK
  CSeq: 3
  Content-Base: rtsp://**************:8554/stream/
  Content-Length: 160
  Content-Type: application/sdp
  Server: gortsplib

  v=0
  o=- 0 0 IN IP4 127.0.0.1
  s=Stream
  c=IN IP4 0.0.0.0
  t=0 0
  m=video 0 RTP/AVP 34
  a=control:rtsp://**************:8554/stream/trackID=0
  a=fmtp:34 cif=1
  SETUP rtsp://**************:8554/stream/trackID=0 RTSP/1.0
  CSeq: 4
  User-Agent: LibVLC/3.0.18 (LIVE555 Streaming Media v2016.11.28)
  Transport: RTP/AVP;unicast;client_port=52282-52283

  RTSP/1.0 200 OK
  CSeq: 4
  Server: gortsplib
  Session: c0fd8ccef6f9439ab9c6103ac9c69d18;timeout=60
  Transport: RTP/AVP;unicast;client_port=52282-52283;server_port=8000-8001;ssrc=E425D518`
  ```
流B:  udp : RTP
  port与流A中的transport中port相同
  通过ssrc,transport中clinet_port,server_port 与流A关联

情况3：
流A ：UDP： SIP
  sdp中存在 `c=IN IP4 ***********`
  sdp中存在 `m= vedio port RTP/AVP payloadType_video`
  sdp中存在 `m= audio port RTP/AVP payloadType_audio`
  sdp中存在 `a= rtpmap:payloadType_video MediaType_video/RATE`
  sdp中存在 `a= rtpmap:payloadType_audio MediaType_audio/RATE`
  DESCRIBE中sdp端口为接收rtp的端口 200OK中sdp中的端口为发送rtp的端口

  ```
  INVITE sip:************:5060 SIP/2.0
  Via: SIP/2.0/UDP ************:51342;rport;branch=z9hG4bKPj0351b238a84e49ca8877caf287c96642
  Max-Forwards: 70
  From: <sip:************>;tag=552ddb95a2434b8aad4997816acc9a4a
  To: <sip:************>
  Contact: <sip:************:51342;ob>
  Call-ID: 3da48b07a489421e8618cf0ff7f28c1c
  CSeq: 21194 INVITE
  Allow: PRACK, INVITE, ACK, BYE, CANCEL, UPDATE, INFO, SUBSCRIBE, NOTIFY, REFER, MESSAGE, OPTIONS
  Supported: replaces, 100rel, timer, norefersub
  Session-Expires: 1800
  Min-SE: 90
  User-Agent: MicroSIP/3.20.7
  Content-Type: application/sdp
  Content-Length:   231

  v=0
  o=- 3904369115 3904369115 IN IP4 ************
  s=pjmedia
  b=AS:218
  t=0 0
  a=X-nat:0
  m=video 4008 RTP/AVP 99
  c=IN IP4 ************
  b=TIAS:128000
  a=sendrecv
  a=rtpmap:99 H264/90000
  a=ssrc:415131524 cname:1b2c5349737970c2
  SIP/2.0 100 Trying
  Via: SIP/2.0/UDP ************:51342;rport=51342;received=************;branch=z9hG4bKPj0351b238a84e49ca8877caf287c96642
  Call-ID: 3da48b07a489421e8618cf0ff7f28c1c
  From: <sip:************>;tag=552ddb95a2434b8aad4997816acc9a4a
  To: <sip:************>
  CSeq: 21194 INVITE
  Content-Length:  0

  SIP/2.0 200 OK
  Via: SIP/2.0/UDP ************:51342;rport=51342;received=************;branch=z9hG4bKPj0351b238a84e49ca8877caf287c96642
  Call-ID: 3da48b07a489421e8618cf0ff7f28c1c
  From: <sip:************>;tag=552ddb95a2434b8aad4997816acc9a4a
  To: <sip:************>;tag=618a26d7d46443998d1eaad75e98bb99
  CSeq: 21194 INVITE
  Contact: <sip:************:53343>
  Allow: PRACK, INVITE, ACK, BYE, CANCEL, UPDATE, INFO, SUBSCRIBE, NOTIFY, REFER, MESSAGE, OPTIONS
  Supported: replaces, 100rel, timer, norefersub
  Session-Expires: 1800;refresher=uac
  Require: timer
  Content-Type: application/sdp
  Content-Length:   231

  v=0
  o=- 3904369114 3904369115 IN IP4 ************
  s=pjmedia
  b=AS:218
  t=0 0
  a=X-nat:0
  m=video 4008 RTP/AVP 99
  c=IN IP4 ************
  b=TIAS:128000
  a=sendrecv
  a=rtpmap:99 H264/90000
  a=ssrc:415131524 cname:249e2b0c11f45dd5
  ```
流B： UDP： RTP
  port与流A中sdp中port与IN IP4 相同
  通过ssrc,sdp中port与流A关联

需求：
将上述三种解析情况都补全
将现有代码中map_ssrc2rtpMediaInfo_与map_port2rtpMediaInfo_合并成为一个map
