

#ifndef __CODECS_IPC_H__
#define __CODECS_IPC_H__

#include <stddef.h>
#include <stdint.h>

#define IPC_DECODER void
#define IPC_CONFIG void

/*======================================================================================================
 * forward declarations of types, enums
 *=====================================================================================================*/

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* +---------------+
* |0|1|2|3|4|5|6|7|
* +-+-+-+-+-+-+-+-+
* |F|NRI|  Type   |
* +---------------+
*/
struct ipc_nalu_t {
  /***************本段为每帧nalu头的*******************/
  uint8_t forbidden_bit : 1;
  uint8_t nal_reference_idc : 2;
  uint8_t nal_unit_type : 5;
  /**********************************/
  uint8_t payload[0];  //nalu payload开始
};

// +-------------------------------+
// | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 |
// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
// |h_dest                 |       |
// +-----------------------|       |
// | h_source      |      LLC      |
// |-------------------------------|
// |  h_proto  |
// +-----------+

enum IPC_PROTOCOL_TYPE {
  IPC_PROTOCOL_UNKNOWN,
  IPC_PROTOCOL_RTSP,
  IPC_PROTOCOL_SIP,
  IPC_PROTOCOL_RTP,
  IPC_PROTOCOL_DHAV,
  IPC_PROTOCOL_MAX
};

enum IPC_FRAME_TYPE_E {
  IPC_NETWORK_ETH,
  IPC_NETWORK_ARM,  // 在ARM设备上获取网卡流量时 如上图所示，存在4字节LLC 需要配置该类型才可以正确使用
};

enum IPC_LOG_LEVEL_E {
  IPC_LOG_Debug,
  IPC_LOG_Error,
  IPC_LOG_Warning,
  IPC_LOG_Info,
};

enum IPC_AAC_E {
  IPC_AAC_NONE,
  IPC_AAC_G711u,
  IPC_AAC_G711a,
  IPC_AAC_G729,
};

enum IPC_AVC_E {
  IPC_AVC_NONE,
  IPC_AVC_H264,
  IPC_AVC_H263,
  IPC_AVC_H265,
};

/**
 * @brief 新nalu流建立时回调函数
 * 
 * @param [out] stream_id  新nalu流的唯一ID
 * @param [out] userdata   初始化decoder时传入的userdata
 */
typedef int (*ipc_on_new_stream)(uint64_t stream_id, void *userdata);

/**
 * @brief 新nalu帧到达时回调函数
 * [注意]：此函数返回的[*nalu_head]指针在离开[ipc_on_new_nalu]作用域后就会失效
 * @param [out] stream_id  新nalu流的唯一ID
 * @param [out] seq        从 0 递增的nalu帧的序号
 * @param [out] nalu_head  nalu结构指针
 * @param [out] nalu_len   nalu结构长度 包含nalu 头
 * @param [out] userdata   初始化decoder时传入的userdata
 */
typedef int (*ipc_on_new_nalu)(uint64_t stream_id, uint64_t seq, struct ipc_nalu_t *nalu_head, uint64_t nalu_len, void *userdata);

/*======================================================================================================
 *  IPC_CFG - 配置可配选项
 *=====================================================================================================*/

#define IPC_DECODER_FLAG_AUDIO (1 << 0)
#define IPC_DECODER_FLAG_VIDEO (1 << 1)

/**
 * @brief 配置可以解析的媒体类型 按位或 输出音频nalu 输出视频nalu
 * 
 * @param cfg 创建的config句柄
 * @param flag 能力flag
 */
void ipc_cfg_set_config_flag(IPC_CONFIG *cfg, uint64_t flag);

/**
 * @brief 设置捕获网络类型
 * ARM设备捕获到的ETH层中存在 'LLC' 的额外字段 需要配置 详细结构见 'IPC_FRAME_TYPE_E' 的备注
 * 默认为正常eth层解析
 * @param cfg 创建的config句柄
 * @param type 网络类型
 */
void ipc_cfg_set_config_frame_type(IPC_CONFIG *cfg, enum IPC_FRAME_TYPE_E type);

/**
 * @brief 设置日志等级
 * 
 * @param cfg 创建的config句柄
 * @param level 日志等级
 */
void ipc_cfg_set_config_log_level(IPC_CONFIG *cfg, enum IPC_LOG_LEVEL_E level);


/**
 * @brief 设置不处理的报文IP
 * 
 * @param cfg 创建的config句柄
 * @param ip 主机字节序
 */
void ipc_cfg_set_exclude_packet_ip(IPC_CONFIG *cfg,uint32_t ip_hl);

/**
 * @brief 设置TCP重组乱序窗口大小
 *
 * @param cfg 创建的config句柄
 * @param window_size TCP重组乱序窗口大小，默认5
 */
extern "C" void ipc_cfg_set_tcp_rsm_out_of_order(IPC_CONFIG *cfg, int window_size);


/*======================================================================================================
 *  ipc_deocder - 当回调函数到达时 从stream中获取高层信息
 *=====================================================================================================*/

/*******************************************************************************************************/
/*                     所有stream都可以获取到的网络信息                                                  */
/*                   以下接口返回的网络信息均为网络字节序                                                 */
/*******************************************************************************************************/

uint32_t ipc_deocder_get_stream_src_ip(IPC_DECODER *decoder, uint64_t stream_id);
uint32_t ipc_deocder_get_stream_dst_ip(IPC_DECODER *decoder, uint64_t stream_id);
uint16_t ipc_deocder_get_stream_src_port(IPC_DECODER *decoder, uint64_t stream_id);
uint16_t ipc_deocder_get_stream_dst_port(IPC_DECODER *decoder, uint64_t stream_id);
uint8_t  ipc_deocder_get_stream_transport_proto(IPC_DECODER *decoder, uint64_t stream_id);  // [6 tcp]/[17 udp]
enum IPC_LOG_LEVEL_E  ipc_deocder_get_config_log_level(IPC_DECODER *decoder);

/*******************************************************************************************************/
/*                 stream通过'ipc_deocder_get_stream_capability' 接口返回的能力获取信息              */
/*******************************************************************************************************/

#define IPC_CAPABILITY_ADVANCED_VIDEO_CODING (1 << 0)
#define IPC_CAPABILITY_ADVANCED_AUDIO_CODING (1 << 1)
#define IPC_CAPABILITY_DEVICEID (1 << 2)
#define IPC_CAPABILITY_IPC_PROTO_TYPE (1 << 3)
#define IPC_CAPABILITY_SAMPLE_RATE (1 << 4)
#define IPC_CAPABILITY_SPS (1 << 5)
#define IPC_CAPABILITY_PPS (1 << 6)

/**
 * @brief 配置用户态上下文
 * 
 * @param cfg 创建的config句柄创建的config句柄
 * @param userdata 用户上下文指针
 */
void ipc_cfg_set_user_data(IPC_CONFIG *cfg_, void *userdata);

/**
 * @brief 返回当前指定stream_id的能力 按位或 指明可以通过get接口获取到stream的什么信息
 * 
 * @param decoder decoder句柄
 * @param stream_id 通过new_stream接口返回的已经存在的stream_id
 * @return uint64_t 
 */
uint64_t ipc_deocder_get_stream_capability(IPC_DECODER *decoder, uint64_t stream_id);

/**
 * @brief 返回当前指定stream_id的媒体类型(视频)
 * 
 * @param decoder decoder句柄
 * @param stream_id 通过new_stream接口返回的已经存在的stream_id
 * @return 
 */
enum IPC_AVC_E ipc_deocder_get_advanced_video_coding(IPC_DECODER *decoder, uint64_t stream_id);

/**
 * @brief 返回当前指定stream_id的媒体类型(音频)
 * 
 * @param decoder decoder句柄
 * @param stream_id 通过new_stream接口返回的已经存在的stream_id
 * @return 
 */
enum IPC_AAC_E ipc_deocder_get_advanced_audio_coding(IPC_DECODER *decoder, uint64_t stream_id);

/**
 * @brief 返回当前指定stream_id的承载层协议
 * 
 * @param decoder decoder句柄
 * @param stream_id 通过new_stream接口返回的已经存在的stream_id
 * @param [inout] buff 
 * @param [in] buff_len  buff的最大长度
 * @return uint8_t
 */
enum IPC_PROTOCOL_TYPE ipc_deocder_get_ipc_protocol(IPC_DECODER *decoder, uint64_t stream_id);

/**
 * @brief 返回当前指定stream_id的采样率
 *
 * @param decoder decoder句柄
 * @param stream_id 通过new_stream接口返回的已经存在的stream_id
 * @return int 采样率数值
 */
int ipc_deocder_get_stream_sample_rate(IPC_DECODER *decoder, uint64_t stream_id);

/**
 * @brief 返回当前活跃流的数量
 *
 * @param decoder decoder句柄
 * @return size_t 当前活跃流的数量
 */
size_t ipc_deocder_get_stream_count(IPC_DECODER *decoder);

/**
 * @brief 返回当前指定stream_id的RTP时间戳
 *
 * @param decoder decoder句柄
 * @param stream_id 通过new_stream接口返回的已经存在的stream_id
 * @return uint32_t RTP时间戳
 */
uint32_t ipc_deocder_get_rtp_timestamp(IPC_DECODER *decoder, uint64_t stream_id);

/**
 * @brief 返回当前指定stream_id的设备类型
 * 
 * @param [in] decoder 
 * @param [in] stream_id 
 * @param [in out] buff  由用户层管理的内存指针
 * @param [in] buff_len  buff的最大长度
 * @return int 拷贝到buff中的长度
 */
int ipc_deocder_get_deviceid(IPC_DECODER *decoder, uint64_t stream_id, uint8_t *buff, size_t buff_len);

/**
 * @brief 返回当前指定stream_id的上一次到达的SPS
 * 
 * @param decoder decoder句柄
 * @param stream_id 通过new_stream接口返回的已经存在的stream_id
 * @param [in out] buff  由用户层管理的内存指针
 * @param [in] buff_len  buff的最大长度
 * @return int 拷贝到buff中的长度
 */
int ipc_deocder_get_stream_latest_sps(IPC_DECODER *decoder, uint64_t stream_id, uint8_t *buff, size_t buff_len);

/**
 * @brief 返回当前指定stream_id的上一次到达的PPS
 * 
 * @param decoder decoder句柄
 * @param stream_id 通过new_stream接口返回的已经存在的stream_id
 * @param [inout] buff 
 * @param [in] buff_len  buff的最大长度
 * @return int 拷贝到buff中的长度
 */
int ipc_deocder_get_stream_latest_pps(IPC_DECODER *decoder, uint64_t stream_id, uint8_t *buff, size_t buff_len);

/*======================================================================================================
 * ipc_DECODER -  create 与 destroy
 *=====================================================================================================*/

/**
 * @brief 创建config句柄
 * 具有通过一系列set接口设置decoder的能力
 * @return IPC_CONFIG* config句柄
 */
IPC_CONFIG *ipc_create_config(ipc_on_new_stream, ipc_on_new_nalu);

/**
 * @brief 通过设置好的IPC_CFG创建decoder句柄
 *  一旦decodeer句柄创建 则不能通过一系列set接口再次设置该decoder的能力
 *  cfg指针移交给decoder管理
 * @param cfg 创建的config句柄
 * @return IPC_DECODER* decoder句柄
 */
IPC_DECODER *ipc_create_decoder(IPC_CONFIG *cfg);

/**
 * @brief 销毁ipc_decoder句柄 同时也会自动销毁IPC_CFG内存
 * 
 */
void ipc_destroy_decoder(IPC_DECODER *);

/*======================================================================================================
 * ipc_DECODER -  处理流量
 *=====================================================================================================*/

/**
 * @brief 调用此接口，需要在IPC_DECODER 初始化后调用
 *
 * @param buff 捕获的网络流量packet
 * @param len  捕获的网络流量packet的长度
 * @return int -1 为IPC_DECODER 未初始化 0为 失败 1为成功
 */
int ipc_process_packet(IPC_DECODER *, const uint8_t *buff, int len);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
