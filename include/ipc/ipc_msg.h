#ifndef __IPC_MSG_H__
#define __IPC_MSG_H__

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief IPC消息类型枚举
 */
enum ipc_msg_type_enum
{
    IPC_MSG_NONE = 0,
    IPC_MSG_HELLO,           // hello消息，程序启动时发送
    IPC_MSG_HEART_BEAT,      // 心跳消息
    IPC_MSG_STREAM_DETECTED, // 流探测成功消息
    IPC_MSG_STREAM_NALU,     // NALU数据消息
    IPC_MSG_DUMP_START,      // 抓包开始消息
    IPC_MSG_DUMP_STOP,       // 抓包停止消息（不需要消息体）
    IPC_MSG_DUMP_DONE,       // 抓包完成消息（不需要消息体）
};

/**
 * @brief IPC消息头结构体
 * 所有消息都以此结构体开头
 */
struct ipc_msg_hdr
{
    uint8_t  proto_version;   // 协议版本，10表示v1.0
    uint8_t  msg_type;        // 消息类型，取值为ipc_msg_type_enum
    uint16_t transaction_id;  // 事务ID，用于配对请求与响应
    uint32_t msg_len;         // 消息体长度，不包含消息头
} __attribute__((packed));

/**
 * @brief Hello消息体
 * 程序启动时发送，通知应用程序已经启动
 */
struct ipc_msg_hello
{
    uint8_t dpi_version;      // 解析程序版本，例如24表示v2.4
    uint8_t plugin_version;   // 插件版本号
    char    plugin_path[128]; // 当前加载的插件路径
} __attribute__((packed));

/**
 * @brief 心跳消息体
 * 定期发送，报告程序状态
 */
struct ipc_msg_heart_beat
{
    uint8_t dpi_status;       // 状态：idle(无流量), probing(探测中), ok(正常解析), error(出错)
    uint8_t stream_cnt;       // 当前流数量
    char    msg[128];         // 补充消息，例如出错信息
} __attribute__((packed));

/**
 * @brief 媒体类型枚举
 * 用于标识视频编码格式
 */
enum media_type_enum
{
    MEDIA_TYPE_UNKNOWN = 0,   // 未知类型
    MEDIA_TYPE_H264,          // H.264编码
    MEDIA_TYPE_H263,          // H.263编码
    MEDIA_TYPE_H265,          // H.265编码
    MEDIA_TYPE_MJPEG,         // MJPEG编码
    MEDIA_TYPE_MPEG4,         // MPEG-4编码
};

/**
 * @brief 流探测成功消息体
 * 当检测到新的视频流时发送
 */
struct ipc_msg_stream_detected
{
    uint32_t streamID;        // 流标识符
    uint32_t flag;            // 预留标志字段，暂时不使用，是否有音频等
    char     proto[16];       // 传输协议，例如'rtsp/rtp', 'dahua'
    uint8_t  media_type;      // 媒体类型，取值为media_type_enum
} __attribute__((packed));

/**
 * @brief NALU数据消息体
 * 传输解码后的NALU帧数据
 */
struct ipc_msg_stream_nalu
{
    uint32_t streamID;        // 流标识符
    uint32_t nalu_seq;        // NALU序号
    uint32_t rtp_timestamp;   // RTP帧时间戳
    uint8_t  nalu_payload[0]; // NALU数据，变长
} __attribute__((packed));

/**
 * @brief 抓包开始消息体
 * 接收到抓包开始指令时的消息
 */
struct ipc_msg_dump_start
{
    uint32_t dump_size;       // 抓包文件大小，单位为字节
    char     brand[64];       // IPC品牌名，用于编码到pcap文件名上
    char     serial[64];      // IPC型号，用于编码到pcap文件名上
} __attribute__((packed));

/**
 * @brief DPI状态枚举
 * 用于心跳消息中的dpi_status字段
 */
enum dpi_status_enum
{
    DPI_STATUS_IDLE = 0,      // 空闲状态，无流量
    DPI_STATUS_PROBING,       // 探测中
    DPI_STATUS_OK,            // 正常解析
    DPI_STATUS_ERROR,         // 出错状态
};

/**
 * @brief 完整的IPC消息结构体
 * 包含消息头和消息体的联合体
 */
struct ipc_message
{
    struct ipc_msg_hdr hdr;
    union {
        struct ipc_msg_hello         hello;
        struct ipc_msg_heart_beat    heart_beat;
        struct ipc_msg_stream_detected stream_detected;
        struct ipc_msg_stream_nalu   stream_nalu;
        struct ipc_msg_dump_start    dump_start;
        uint8_t raw_data[0];         // 原始数据访问
    } body;
} __attribute__((packed));

#ifdef __cplusplus
}
#endif

#endif /* __IPC_MSG_H__ */
